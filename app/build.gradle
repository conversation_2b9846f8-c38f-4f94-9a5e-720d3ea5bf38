import java.text.SimpleDateFormat

plugins {
    alias(libs.plugins.android.application)
}

def getGitHash = { ->
    def stdout = new ByteArrayOutputStream()
    exec {
        commandLine 'git', 'rev-parse', '--short', 'HEAD'
        standardOutput = stdout
    }
    return stdout.toString().trim()
}

def getGitBranch = { ->
    def stdout = new ByteArrayOutputStream()
    exec {
        commandLine 'git', 'rev-parse', '--abbrev-ref', 'HEAD'
        standardOutput = stdout
    }
    return stdout.toString().trim()
}

def getBuildInfo = { ->
    return "代码分支: " + getGitBranch() + "-" + getGitHash() + "\\n编译时间: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())
}

android {
    def appVersionName = '1.3.3'
    def appVersionCode = 19
    def appPackageName = 'io.github.netamade'

    namespace "${appPackageName}"
    compileSdk 35
    //noinspection GradleDependency

    defaultConfig {
        applicationId "${appPackageName}"
        minSdk 30
        //noinspection ExpiredTargetSdkVersion
        targetSdk 30
        versionCode appVersionCode
        versionName "${appVersionName}"

        resValue 'string', 'package_name', applicationId
        resValue 'string', 'git_hash', getGitHash()
        resValue 'string', 'git_branch', getGitBranch()
        resValue 'string', 'build_time', String.valueOf(System.currentTimeMillis())
        resValue 'string', 'build_info', getBuildInfo() + "\\n编译版本: " + versionName + " " + versionCode

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        manifestPlaceholders = [appName: "哪吒美式", appIcon: "@drawable/ic_launcher"]

        ndk {
            abiFilters 'arm64-v8a', 'x86_64'
        }
    }

    signingConfigs {
        release {
            storeFile file("netamade.keystore")
            storePassword "li123321"
            keyAlias "key0"
            keyPassword "li123321"
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
        }
        debug {
            signingConfig signingConfigs.release
        }
    }

    applicationVariants.configureEach { variant ->
        def packageName = variant.applicationId
        def versionName = variant.versionName
        def versionCode = variant.versionCode
        def buildTypeName = variant.name.capitalize()

        def newFileName = "哪吒美式_${packageName}-${versionName}-${versionCode}.apk"

        variant.outputs.configureEach { output ->

            output.outputFileName = newFileName
        }

        variant.assembleProvider.configure { assemble ->

            def outdir = file("${project.rootDir}/apk-releases/v${appVersionName}_${appVersionCode}")
            if (variant.buildType.name == "debug") {
                outdir = file(outdir.getPath() + "-debug")
            }
            outdir.mkdirs()

            assemble.doLast {
                copy {
                    from variant.outputs*.outputFile
                    into outdir
                }
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    buildFeatures {
        viewBinding true
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    lint {
        checkReleaseBuilds = false
    }
}

dependencies {
    implementation project(":hidden-api-exemptions")

    implementation libs.customactivityoncrash
    implementation libs.appcompat
    implementation libs.coordinatorlayout
    implementation libs.preference
    implementation libs.material
    implementation libs.activity
    implementation libs.navigation.fragment
    implementation libs.navigation.ui
    implementation libs.gson
    implementation libs.vertical.stepper.form

    compileOnly libs.lombok
    annotationProcessor libs.lombok

    compileOnly fileTree("libs/compile-only")
//    implementation fileTree("libs/compile")
    implementation libs.multitype
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
    implementation libs.libsu

    implementation(platform(libs.kotlin.bom))


}