<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="ProtectedPermissions,QueryAllPackagesPermission">

    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="com.hozon.recommend.script" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="Manifest.permission.BLUETOOTH_PRIVILEGED" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.INJECT_EVENTS" />
    <uses-permission android:name="android.permission.REQUEST_NETWORK_SCORES" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.BACKUP" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CLEAR_APP_USER_DATA" />
    <uses-permission android:name="android.permission.DELETE_CACHE_FILES" />
    <uses-permission android:name="android.permission.DUMP" />
    <uses-permission android:name="android.permission.FORCE_STOP_PACKAGES" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS_PRIVILEGED" />
    <uses-permission android:name="android.permission.GET_PACKAGE_SIZE" />
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" />
    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" />
    <uses-permission android:name="android.permission.MANAGE_USERS" />
    <uses-permission android:name="android.permission.MASTER_CLEAR" />
    <uses-permission android:name="android.permission.NETWORK_SETTINGS" />
    <uses-permission android:name="android.permission.OVERRIDE_WIFI_CONFIG" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.REBOOT" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.SET_PREFERRED_APPLICATIONS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.START_FOREGROUND" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.CONTROL_DISPLAY_BRIGHTNESS" />
    <uses-permission android:name="android.car.permission.CONTROL_CAR_CLIMATE" />
    <uses-permission android:name="android.car.permission.CAR_VENDOR_EXTENSION" />
    <uses-permission android:name="android.car.permission.CONTROL_CARLAN" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.Manifest.permission.NETWORK_SETTINGS" />
    <uses-permission android:name="android.Manifest.permission.UPDATE_DEVICE_STATS" />
    <uses-permission android:name="com.tencent.wecarflow.PLAY_CONTROL" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="com.tencent.wecar.music.PLAY_CONTROL" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.android.car.permission.CAR_HOZON_CONTROL_AUDIO_EFFECT" />
    <uses-permission
        android:name="com.hozon.permission.push"
        android:protectionLevel="normal" />
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="com.hozon.pkiservice.permission.ACCESS_PKI_SERVICE" />
    <uses-permission android:name="android.car.permission.CAR_HOZON_CONTROL_AUDIO_EFFECT" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES" />
    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
    <uses-permission android:name="android.permission.DELETE_PACKAGES" />
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" />
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS_FULL" />
    <uses-permission android:name="android.permission.INTERACT_ACROSS_PROFILES" />


    <application
        android:name=".AppContext"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="${appIcon}"
        android:label="${appName}"
        android:supportsRtl="true"
        android:theme="@style/MyAppTheme"
        tools:replace="label,icon"
        tools:targetApi="31">

        <uses-library
            android:name="com.hozonauto.vehiclelan.proxy-V1.0-java"
            android:required="false" />

        <activity
            android:name=".GrantActivity"
            android:clearTaskOnLaunch="true"
            android:theme="@style/MyAppDialogTheme" />

        <activity
            android:name=".NullActivity"
            android:exported="true"
            android:theme="@android:style/Theme.NoDisplay">

        </activity>

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:icon="@drawable/ic_launcher"
            android:label="@string/app_name"
            android:launchMode="singleInstance"
            android:theme="@style/MyAppTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".TencentMap"
            android:exported="true"
            android:icon="@drawable/ic_tencent"
            android:label="@string/tencent_map"
            android:launchMode="singleTask"
            android:theme="@style/MyAppDialogTheme">

        </activity>
        <activity
            android:name=".BaiduMap"
            android:exported="true"
            android:icon="@drawable/ic_baidu"
            android:label="@string/baidu_map"
            android:launchMode="singleTask"
            android:theme="@style/MyAppDialogTheme">

        </activity>

        <activity
            android:name=".AMap"
            android:exported="true"
            android:icon="@drawable/ic_amap"
            android:label="@string/amap_map"
            android:launchMode="singleTask"
            android:theme="@style/MyAppDialogTheme">

        </activity>

        <activity
            android:name=".AMapMobile"
            android:exported="true"
            android:icon="@drawable/ic_amap_mobile"
            android:label="@string/amap_map_mobile"
            android:launchMode="singleTask"
            android:theme="@style/MyAppDialogTheme">

        </activity>

        <receiver
            android:name=".receiver.NetamadeReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.SCREEN_ON" />
                <action android:name="io.github.netamade.LAUNCH_HOZON_MAP" />
            </intent-filter>
        </receiver>

        <service
            android:name=".service.AnyTouchService"
            android:enabled="true"
            android:exported="true"
            android:foregroundServiceType="mediaPlayback"
            android:label="@string/app_name"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>

            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/anytouch_accessibility_service_config" />
        </service>

    </application>

</manifest>