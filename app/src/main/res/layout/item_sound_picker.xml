<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:strokeWidth="1dp"
    app:strokeColor="?attr/colorOutlineVariant"
    app:cardBackgroundColor="?attr/colorSurface">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical">

        <!-- 选择状态指示器 -->
        <com.google.android.material.radiobutton.MaterialRadioButton
            android:id="@+id/radioButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp" />

        <!-- 音频图标 -->
        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_volume_up"
            android:layout_marginEnd="12dp"
            app:tint="?attr/colorPrimary" />

        <!-- 文件名 -->
        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/tvFileName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="音频文件名.mp3"
            android:textSize="16sp"
            android:textColor="?attr/colorOnSurface"
            android:ellipsize="end"
            android:maxLines="1" />

        <!-- 播放按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnPlay"
            style="@style/Widget.Material3.Button.IconButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="4dp"
            app:icon="@drawable/ic_play_arrow"
            app:iconTint="?attr/colorOnSurfaceVariant"
            android:contentDescription="播放音频" />

        <!-- 删除按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnDelete"
            style="@style/Widget.Material3.Button.IconButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            app:icon="@drawable/ic_delete"
            app:iconTint="?attr/colorError"
            android:contentDescription="删除音频" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
