<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingBottom="80dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    <!-- 添加自定义事件按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAddEvent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:layout_marginBottom="80dp"
        android:src="@drawable/ic_add"
        android:contentDescription="添加自定义事件"
        app:tint="@android:color/white" />

    <!-- 重置按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabReset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|start"
        android:layout_margin="16dp"
        android:src="@drawable/ic_refresh"
        android:contentDescription="重置到默认设置"
        app:tint="@android:color/white"
        app:backgroundTint="?attr/colorSecondary" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>