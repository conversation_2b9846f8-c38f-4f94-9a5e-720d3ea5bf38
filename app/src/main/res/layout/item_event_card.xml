<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:strokeWidth="0dp"
    app:cardBackgroundColor="?attr/colorSurface">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 左侧内容区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 第一行：description 主标题 -->
            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/tvDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="事件描述"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="?attr/colorOnSurface"
                android:layout_marginBottom="4dp" />

            <!-- 第二行：tag 和 message -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/tvTag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="标签"
                    android:textSize="14sp"
                    android:textColor="?attr/colorPrimary"
                    android:background="@drawable/bg_tag_chip"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp"
                    android:layout_marginEnd="8dp" />

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/tvMessage"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="消息内容"
                    android:textSize="14sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:ellipsize="end"
                    android:maxLines="1" />

            </LinearLayout>

            <!-- 第三行：音频选择区域 -->
            <LinearLayout
                android:id="@+id/layoutSoundSelector"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:clickable="true"
                android:focusable="true"
                android:padding="8dp"
                android:background="@drawable/bg_sound_selector"
                android:foreground="?attr/selectableItemBackground">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_volume_up"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="8dp"
                    app:tint="?attr/colorOnSurfaceVariant" />

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/tvSoundFile"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="点击选择播放的音频"
                    android:textSize="14sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:layout_gravity="center_vertical"
                    android:ellipsize="end"
                    android:maxLines="1" />

            </LinearLayout>

        </LinearLayout>

        <!-- 右侧开关 -->
        <com.google.android.material.materialswitch.MaterialSwitch
            android:id="@+id/switchEnabled"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="16dp" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
