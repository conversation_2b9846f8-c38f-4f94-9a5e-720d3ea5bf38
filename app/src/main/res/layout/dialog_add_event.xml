<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="事件描述"
        app:boxStrokeColor="?attr/colorPrimary"
        app:hintTextColor="?attr/colorPrimary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="标签"
        app:boxStrokeColor="?attr/colorPrimary"
        app:hintTextColor="?attr/colorPrimary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etTag"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="消息内容"
        app:boxStrokeColor="?attr/colorPrimary"
        app:hintTextColor="?attr/colorPrimary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textMultiLine"
            android:maxLines="3" />

    </com.google.android.material.textfield.TextInputLayout>

</LinearLayout>
