<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 标题 -->
    <com.google.android.material.textview.MaterialTextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="选择音频文件"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="?attr/colorOnSurface"
        android:layout_marginBottom="16dp"
        android:gravity="center" />

    <!-- 搜索框和添加按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="搜索音频文件"
            app:boxStrokeColor="?attr/colorPrimary"
            app:hintTextColor="?attr/colorPrimary"
            app:startIconDrawable="@drawable/ic_search">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etSearch"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnAddAudio"
            style="@style/Widget.Material3.Button.IconButton"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginStart="8dp"
            app:icon="@drawable/ic_add"
            app:iconTint="?attr/colorPrimary"
            android:contentDescription="添加音频文件" />

    </LinearLayout>

    <!-- RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:maxHeight="400dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    <!-- 空状态提示 -->
    <LinearLayout
        android:id="@+id/emptyStateLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="32dp"
        android:visibility="gone">

        <ImageView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:src="@drawable/ic_volume_up"
            android:layout_marginBottom="16dp"
            app:tint="?attr/colorOnSurfaceVariant" />

        <com.google.android.material.textview.MaterialTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="暂无音频文件"
            android:textSize="16sp"
            android:textColor="?attr/colorOnSurfaceVariant" />

    </LinearLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end"
        android:layout_marginTop="16dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnCancel"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="取消"
            android:layout_marginEnd="8dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnConfirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="确定"
            android:enabled="false" />

    </LinearLayout>

</LinearLayout>
