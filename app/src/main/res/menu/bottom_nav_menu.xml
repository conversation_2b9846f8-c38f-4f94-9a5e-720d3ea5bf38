<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android">

    <item
        android:id="@+id/navigation_auto_grant"
        android:icon="@drawable/ic_automate"
        android:title="@string/auto_grant" />

    <item
        android:id="@+id/navigation_accessibility"
        android:icon="@drawable/ic_accessibility"
        android:title="@string/accessibility_service" />

    <item
        android:id="@+id/navigation_gesture"
        android:icon="@drawable/ic_gesture"
        android:title="@string/any_touch_gesture" />

    <item
        android:id="@+id/navigation_app_icon_editor"
        android:icon="@drawable/ic_app_editor"
        android:title="@string/app_icon_editor" />

    <item
        android:id="@+id/navigation_user_app"
        android:icon="@drawable/ic_apps"
        android:title="@string/user_apps_list" />

    <item
        android:id="@+id/navigation_syslog"
        android:icon="@drawable/ic_sound"
        android:title="@string/happy_sound" />

<!--    <item-->
<!--        android:id="@+id/navigation_syslog"-->
<!--        android:icon="@drawable/ic_syslog"-->
<!--        android:title="@string/syslog" />-->

    <item
        android:id="@+id/navigation_app_debug"
        android:icon="@drawable/ic_debug"
        android:title="@string/app_debug" />

    <item
        android:id="@+id/navigation_experimental_feature"
        android:icon="@drawable/ic_testing"
        android:title="@string/experimental_feature" />

</menu>