<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:summary="@string/auto_grant_text"
    android:title="@string/auto_grant">

    <io.github.netamade.ui.preference.GrantAndLaunchMapPreference
        android:key="grant_and_launch_map"
        android:summary="@string/grant_and_launch_map_text"
        android:title="@string/grant_and_launch_map" />

    <io.github.netamade.ui.preference.HighlightPreferenceScreen
        android:key="grant_now"
        android:summary="@string/grant_now_text"
        android:title="@string/grant_now"
        app:iconSpaceReserved="false" />


    <io.github.netamade.ui.preference.HighlightPreferenceScreen
        android:key="countdown_grant"
        android:summary="@string/countdown_grant_text"
        android:title="@string/countdown_grant"
        app:iconSpaceReserved="false" />

    <io.github.netamade.ui.preference.HighlightPreferenceScreen
        android:summary="@string/copyright_text"
        android:title="@string/copyright_text"
        app:iconSpaceReserved="false" />

    <io.github.netamade.ui.preference.ExpandablePreferenceCategory
        android:key="category_auto_grant_screen_on"
        android:summary="@string/auto_grant_screen_on_text"
        android:title="@string/auto_grant_screen_on"
        app:alwaysCollapse="true"
        app:childKeys="auto_grant_enabled_screen_on,auto_grant_countdown_seconds_screen_on,auto_grant_selected_map_screen_on,auto_grant_launch_map_screen_on,auto_grant_clear_task_screen_on,auto_grant_unlock_swckey_state_screen_on,block_aispeech_network_screen_on,auto_grant_launch_third_party_apps_screen_on,auto_grant_third_party_app_interval_screen_on,auto_grant_selected_third_party_apps_screen_on"
        app:iconSpaceReserved="false">

        <SwitchPreferenceCompat
            android:key="auto_grant_enabled_screen_on"
            android:switchTextOff="@string/disabled"
            android:switchTextOn="@string/enabled"
            android:title="@string/auto_grant_event_enabled"
            app:defaultValue="true"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:defaultValue="true"
            android:dependency="auto_grant_enabled_screen_on"
            android:key="auto_grant_launch_map_screen_on"
            android:summary="@string/auto_grant_launch_map_text"
            android:title="@string/auto_grant_launch_map"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:defaultValue="true"
            android:dependency="auto_grant_enabled_screen_on"
            android:key="block_aispeech_network_screen_on"
            android:summary="@string/block_aispeech_network_text"
            android:title="@string/block_aispeech_network"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:defaultValue="false"
            android:dependency="auto_grant_enabled_screen_on"
            android:key="auto_grant_unlock_swckey_state_screen_on"
            android:summary="@string/swckey_state_text"
            android:title="@string/swckey_state"
            app:iconSpaceReserved="false" />


        <ListPreference
            android:dependency="auto_grant_enabled_screen_on"
            android:entries="@array/auto_map_entries"
            android:entryValues="@array/auto_map_values"
            android:key="auto_grant_selected_map_screen_on"
            android:summary="@string/auto_grant_target_map_text"
            android:title="@string/auto_grant_target_map"
            app:defaultValue="none"
            app:iconSpaceReserved="false" />

        <io.github.netamade.ui.preference.IndicatedSeekBarPreference
            android:dependency="auto_grant_enabled_screen_on"
            android:key="auto_grant_countdown_seconds_screen_on"
            android:max="600"
            android:summary="@string/fmt_auto_grant_countdown_seconds"
            android:title="@string/auto_grant_countdown_seconds"
            app:defaultValue="10"
            app:iconSpaceReserved="false"
            app:min="0"
            app:showSeekBarValue="true" />

        <!-- 应用启动设置 -->
        <SwitchPreferenceCompat
            android:defaultValue="false"
            android:dependency="auto_grant_enabled_screen_on"
            android:key="auto_grant_launch_third_party_apps_screen_on"
            android:summary="在启动目标地图后，自动启动选中的应用"
            android:title="启动应用"
            app:iconSpaceReserved="false" />

        <io.github.netamade.ui.preference.IndicatedSeekBarPreference
            android:dependency="auto_grant_launch_third_party_apps_screen_on"
            android:key="auto_grant_third_party_app_interval_screen_on"
            android:max="300"
            android:summary="应用启动间隔：%d 秒"
            android:title="启动间隔"
            app:defaultValue="3"
            app:iconSpaceReserved="false"
            app:min="1"
            app:showSeekBarValue="true" />

        <Preference
            android:dependency="auto_grant_launch_third_party_apps_screen_on"
            android:key="auto_grant_selected_third_party_apps_screen_on"
            android:summary="点击选择要启动的应用"
            android:title="选择应用"
            app:iconSpaceReserved="false" />

    </io.github.netamade.ui.preference.ExpandablePreferenceCategory>


</PreferenceScreen>
