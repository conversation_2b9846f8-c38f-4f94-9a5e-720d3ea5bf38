package io.github.netamade.sound;

import java.io.File;
import java.util.List;

import io.github.netamade.AppContext;
import lombok.Getter;

public class SoundManager {

    @Getter
    private static File soundStorageDir;

    static {
        soundStorageDir = new File(AppContext.applicationContext().getFilesDir(), "sounds");
        soundStorageDir.mkdirs();
    }

    public static void playSound(String soundName) {
        File soundFile = new File(soundStorageDir, soundName);
        if (!soundFile.exists()) {
            return;
        }
        //todo
    }

    public static void init() {
        // todo
    }

    public static void deleteSound(String soundName) {
        //todo
    }

    public static List<String> listSounds() {
        //todo
        return null;
    }

}
