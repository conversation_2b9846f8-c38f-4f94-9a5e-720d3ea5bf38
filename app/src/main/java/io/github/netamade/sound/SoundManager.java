package io.github.netamade.sound;

import android.content.res.AssetManager;
import android.media.MediaPlayer;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import io.github.netamade.AppContext;
import io.github.netamade.AppSettings;
import lombok.Getter;

public class SoundManager {
    private static final String TAG = "SoundManager";
    private static MediaPlayer mediaPlayer;

    @Getter
    private static File soundStorageDir;

    static {
        soundStorageDir = new File(AppContext.applicationContext().getFilesDir(), "sounds");
        soundStorageDir.mkdirs();
    }

    public static File getSoundPath(String soundName) {
        return new File(soundStorageDir, soundName);
    }

    public static void playSound(String soundName) {
        File soundFile = new File(soundStorageDir, soundName);
        if (!soundFile.exists()) {
            Log.w(TAG, "Sound file not found: " + soundName);
            return;
        }

        try {
            // 停止之前的播放
            if (mediaPlayer != null) {
                mediaPlayer.stop();
                mediaPlayer.release();
            }

            mediaPlayer = new MediaPlayer();
            mediaPlayer.setVolume(1f, 1f);
            mediaPlayer.setDataSource(soundFile.getAbsolutePath());
            mediaPlayer.prepare();
            mediaPlayer.start();

            // 播放完成后释放资源
            mediaPlayer.setOnCompletionListener(mp -> {
                mp.release();
                mediaPlayer = null;
            });

        } catch (IOException e) {
            Log.e(TAG, "Error playing sound: " + soundName, e);
        }
    }

    public static void init() {
        if (AppSettings.isSoundLibraryInitialized()) {
            return;
        }

        try {
            AssetManager assetManager = AppContext.applicationContext().getAssets();
            String[] soundFiles = assetManager.list("sounds");

            if (soundFiles != null) {
                for (String fileName : soundFiles) {
                    copyAssetToStorage(assetManager, "sounds/" + fileName, fileName);
                }
            }

            AppSettings.setSoundLibraryInitialized(true);
            Log.i(TAG, "Sound library initialized successfully");

        } catch (IOException e) {
            Log.e(TAG, "Error initializing sound library", e);
        }
    }

    private static void copyAssetToStorage(AssetManager assetManager, String assetPath, String fileName) {
        File targetFile = new File(soundStorageDir, fileName);
        if (targetFile.exists()) {
            return; // 文件已存在，跳过
        }

        try (InputStream inputStream = assetManager.open(assetPath);
             FileOutputStream outputStream = new FileOutputStream(targetFile)) {

            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
            }

            Log.d(TAG, "Copied sound file: " + fileName);

        } catch (IOException e) {
            Log.e(TAG, "Error copying sound file: " + fileName, e);
        }
    }

    public static void deleteSound(String soundName) {
        File soundFile = new File(soundStorageDir, soundName);
        if (soundFile.exists()) {
            boolean deleted = soundFile.delete();
            if (deleted) {
                Log.i(TAG, "Deleted sound file: " + soundName);
            } else {
                Log.w(TAG, "Failed to delete sound file: " + soundName);
            }
        }
    }

    public static List<String> listSounds(String keyword) {
        File[] files = soundStorageDir.listFiles();
        if (files == null) {
            return new ArrayList<>();
        }

        List<String> soundFiles = Arrays.stream(files)
                .filter(File::isFile)
                .map(File::getName)
                .filter(name -> {
                    String lowerName = name.toLowerCase();
                    return lowerName.endsWith(".mp3") ||
                            lowerName.endsWith(".wav") ||
                            lowerName.endsWith(".m4a") ||
                            lowerName.endsWith(".aac");
                })
                .sorted() // 按文件名排序
                .collect(Collectors.toList());

        if (keyword != null && !keyword.trim().isEmpty()) {
            String lowerKeyword = keyword.toLowerCase();
            soundFiles = soundFiles.stream()
                    .filter(name -> name.toLowerCase().contains(lowerKeyword))
                    .collect(Collectors.toList());
        }

        return soundFiles;
    }

    public static boolean copyExternalAudioFile(File sourceFile, String targetFileName) {
        if (sourceFile == null || !sourceFile.exists() || !sourceFile.isFile()) {
            Log.w(TAG, "Source file does not exist or is not a file: " + sourceFile);
            return false;
        }

        File targetFile = new File(soundStorageDir, targetFileName);
        if (targetFile.exists()) {
            Log.w(TAG, "Target file already exists: " + targetFileName);
            return false;
        }

        try (InputStream inputStream = new java.io.FileInputStream(sourceFile);
             FileOutputStream outputStream = new FileOutputStream(targetFile)) {

            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
            }

            Log.i(TAG, "Successfully copied audio file: " + targetFileName);
            return true;

        } catch (IOException e) {
            Log.e(TAG, "Error copying audio file: " + targetFileName, e);
            return false;
        }
    }

    public static void stopPlaying() {
        if (mediaPlayer != null) {
            mediaPlayer.stop();
            mediaPlayer.release();
            mediaPlayer = null;
        }
    }
}
