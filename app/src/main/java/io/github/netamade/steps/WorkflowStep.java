package io.github.netamade.steps;

import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.View;
import android.widget.TextView;

import ernestoyaquello.com.verticalstepperform.Step;
import io.github.netamade.util.ThrowableUtils;

public abstract class WorkflowStep extends Step<String> {

    protected final Handler handler;
    private boolean executed;
    private IsDataValid isDataValid;
    private TextView textView;
    private final StringBuilder text = new StringBuilder();

    protected WorkflowStep(String title, Handler handler) {
        super(title, "");
        this.handler = handler;
    }

    protected void updateTitle(String title) {
        super.updateTitle(title, true);
    }

    protected void sendMessage(int what) {
        handler.sendMessage(Message.obtain(handler, what, this));
    }

    protected void markStepDone(IsDataValid isDataValid) {
        this.isDataValid = isDataValid;
        markAsCompletedOrUncompleted(true);
        if (isDataValid.isValid()) {
            sendMessage(StepMessage.GOTO_NEXT_STEP);
        }
    }

    protected String getString(int resId) {
        return getContext().getString(resId);
    }

    protected String getString(int resId, Object... args) {
        return getContext().getString(resId, args);
    }

    @Override
    protected View createStepContentLayout() {
        textView = new TextView(getContext());
        textView.setTextSize(18);
        return textView;
    }

    protected abstract void execute();

    protected void updateText(String text, boolean append) {
        if (!append) {
            this.text.setLength(0);
        } else if (this.text.length() > 0) {
            this.text.append('\n');
        }
        this.text.append(text);
        textView.setText(this.text.toString());
    }

    @Override
    public String getStepData() {
        return null;
    }

    @Override
    public String getStepDataAsHumanReadableString() {
        return "";
    }

    @Override
    protected void restoreStepData(String data) {

    }

    @Override
    protected IsDataValid isStepDataValid(String stepData) {
        return isDataValid;
    }


    @Override
    protected void onStepOpened(boolean animated) {
        if (executed || !animated) {
            return;
        }

        executed = true;
        try {
            execute();
            markStepDone(new IsDataValid(true));
        } catch (Exception e) {
            if (e instanceof StepException) {
                markStepDone(new IsDataValid(false, e.getMessage()));
            } else {
                Log.i(getTitle(), "execute failed: " + e.getMessage(), e);
                String errorMsg = ThrowableUtils.toString(e);
                markStepDone(new IsDataValid(false, errorMsg));
            }
        }
    }

    @Override
    protected void onStepClosed(boolean animated) {

    }

    @Override
    protected void onStepMarkedAsCompleted(boolean animated) {

    }

    @Override
    protected void onStepMarkedAsUncompleted(boolean animated) {

    }
}
