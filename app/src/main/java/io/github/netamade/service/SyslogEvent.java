package io.github.netamade.service;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class SyslogEvent {

    private String key;
    private String description;
    private String tag;
    private String msg;
    private String sound;
    private boolean enabled;


    // 为了向后兼容，保留 getName() 方法
    public String getName() {
        return description;
    }

    // 为了向后兼容，保留 name() 方法
    public String name() {
        return key;
    }

    // 车辆基本操作
    public static final SyslogEvent LockCar = new SyslogEvent(
            "LockCar", "锁车", "dms_service_app", "power_mode 0", null, false);

    // 车门操作 - 哪吒S
    public static final SyslogEvent DoorRow1LeftOpen = new SyslogEvent(
            "DoorRow1LeftOpen", "开主驾门", "VehicleControllerlmpl", "onChangeEvent DOOR_ROW_1_LEFT:1", null, false);
    public static final SyslogEvent DoorRow1LeftClose = new SyslogEvent(
            "DoorRow1LeftClose", "关主驾门", "VehicleControllerlmpl", "onChangeEvent DOOR_ROW_1_LEFT:0", null, false);
    public static final SyslogEvent DoorRow1RightOpen = new SyslogEvent(
            "DoorRow1RightOpen", "开副驾门", "VehicleControllerlmpl", "onChangeEvent DOOR_ROW_1_RIGHT:1", null, false);
    public static final SyslogEvent DoorRow1RightClose = new SyslogEvent(
            "DoorRow1RightClose", "关副驾门", "VehicleControllerlmpl", "onChangeEvent DOOR_ROW_1_RIGHT:0", null, false);

    // 挂档操作
    public static final SyslogEvent GearPark = new SyslogEvent(
            "GearPark", "挂档-P档", "EnergyBackGroundData", "HZ_PWRSYS_VCU_READY_LIGHT_STATUS 0", null, false);
    public static final SyslogEvent GearDrive = new SyslogEvent(
            "GearDrive", "挂档-D档", "EnergyBackGroundData", "HZ_PWRSYS_VCU_READY_LIGHT_STATUS 1", null, false);

    // 驾驶位操作
    public static final SyslogEvent DriverEnter = new SyslogEvent(
            "DriverEnter", "驾驶位进入", "dms_service_app", "checkHasDriver", null, false);
    public static final SyslogEvent DriverLeave = new SyslogEvent(
            "DriverLeave", "驾驶位离开", "BiologicalOms", "hasDriver state 0", null, false);

    // 座位检测 - 副驾驶位
    public static final SyslogEvent SeatCopilotOccupied = new SyslogEvent(
            "SeatCopilotOccupied", "座位检测-副驾有人", "dms_service_app", "hasDriver: 0 areald 4 once true", null, false);
    public static final SyslogEvent SeatCopilotEmpty = new SyslogEvent(
            "SeatCopilotEmpty", "座位检测-副驾无人", "dms_service_app", "hasDriver: 1 areald 4 once true", null, false);

    // 座位检测 - 后排左侧
    public static final SyslogEvent SeatRearLeftOccupied = new SyslogEvent(
            "SeatRearLeftOccupied", "座位检测-后排左侧有人", "dms_service_app", "hasDriver: 0 areald 16 once true", null, false);
    public static final SyslogEvent SeatRearLeftEmpty = new SyslogEvent(
            "SeatRearLeftEmpty", "座位检测-后排左侧无人", "dms_service_app", "hasDriver: 1 areald 16 once true", null, false);

    // 座位检测 - 后排中间
    public static final SyslogEvent SeatRearCenterOccupied = new SyslogEvent(
            "SeatRearCenterOccupied", "座位检测-后排中间有人", "dms_service_app", "hasDriver: 0 areald 32 once true", null, false);
    public static final SyslogEvent SeatRearCenterEmpty = new SyslogEvent(
            "SeatRearCenterEmpty", "座位检测-后排中间无人", "dms_service_app", "hasDriver: 1 areald 32 once true", null, false);

    // 座位检测 - 后排右侧
    public static final SyslogEvent SeatRearRightOccupied = new SyslogEvent(
            "SeatRearRightOccupied", "座位检测-后排右侧有人", "dms_service_app", "hasDriver: 0 areald 64 once true", null, false);
    public static final SyslogEvent SeatRearRightEmpty = new SyslogEvent(
            "SeatRearRightEmpty", "座位检测-后排右侧无人", "dms_service_app", "hasDriver: 1 areald 64 once true", null, false);

    // 系统初始化
    public static final SyslogEvent WifiInitializing = new SyslogEvent(
            "WifiInitializing", "车辆座舱自检-WiFi初始化", "WifiHAL", "Initializing wifi", null, false);

    // 后备箱操作
    public static final SyslogEvent TrunkOpen = new SyslogEvent(
            "TrunkOpen", "开后备箱", "MultiWindowManager", "HZ_REAR_DOOR_ON: 1", null, false);
    public static final SyslogEvent TrunkClose = new SyslogEvent(
            "TrunkClose", "关后备箱", "MultiWindowManager", "HZ_REAR_DOOR_STATUS: 0", null, false);

    // 静态列表包含所有事件，用于替代 values() 方法
    public static final List<SyslogEvent> ALL_EVENTS = new ArrayList<>(Arrays.asList(
            LockCar,
            DoorRow1LeftOpen, DoorRow1LeftClose, DoorRow1RightOpen, DoorRow1RightClose,
            GearPark, GearDrive,
            DriverEnter, DriverLeave,
            SeatCopilotOccupied, SeatCopilotEmpty,
            SeatRearLeftOccupied, SeatRearLeftEmpty,
            SeatRearCenterOccupied, SeatRearCenterEmpty,
            SeatRearRightOccupied, SeatRearRightEmpty,
            WifiInitializing,
            TrunkOpen, TrunkClose
    ));

    // 为了向后兼容，提供 values() 方法
    public static SyslogEvent[] values() {
        return ALL_EVENTS.toArray(new SyslogEvent[0]);
    }

    @Override
    public String toString() {
        return "SyslogEvent{" +
                "key='" + key + '\'' +
                ", description='" + description + '\'' +
                ", tag='" + tag + '\'' +
                ", msg='" + msg + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        SyslogEvent that = (SyslogEvent) obj;
        return key.equals(that.key);
    }

    @Override
    public int hashCode() {
        return key.hashCode();
    }
}
