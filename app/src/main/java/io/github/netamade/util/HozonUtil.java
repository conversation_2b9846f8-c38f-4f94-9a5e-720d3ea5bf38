package io.github.netamade.util;

import android.os.SystemProperties;
public class HozonUtil {
    public static int getCarCode() {
        int i = SystemProperties.getInt("ro.hozon.car.code", 0);
        return i;
    }

    public static boolean isL() {
        return getCarCode() == 5;
    }

    public static boolean isS() {
        return getCarCode() == 4;
    }

    public static boolean is40() {
        return getCarCode() == 0;
    }

}
