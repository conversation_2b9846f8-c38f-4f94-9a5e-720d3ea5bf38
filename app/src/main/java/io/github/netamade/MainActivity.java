package io.github.netamade;

import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;

import androidx.activity.OnBackPressedCallback;
import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;

import java.util.HashMap;
import java.util.Map;

import io.github.netamade.databinding.ActivityMainBinding;
import io.github.netamade.ui.RewardDialog;
import io.github.netamade.ui.fragment.AppAutoGrantFragment;
import io.github.netamade.ui.fragment.AppDebugFragment;
import io.github.netamade.ui.fragment.AppGestureFragment;
import io.github.netamade.ui.fragment.AppIconEditorFragment;
import io.github.netamade.ui.fragment.AppKeepAccessibilityFragment;
import io.github.netamade.ui.fragment.ExperimentalFeatureFragment;
import io.github.netamade.ui.fragment.HappySoundFragment;
import io.github.netamade.ui.fragment.SysLogFragment;
import io.github.netamade.ui.fragment.Titleable;
import io.github.netamade.ui.fragment.UserAppFragment;
import io.github.netamade.util.ReflectUtils;

public class MainActivity extends BaseActivity {

    public static final String TAG = MainActivity.class.getSimpleName();
    private ActivityMainBinding binding;

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        int menuId = getIntent().getIntExtra("menuId", -1);

        getOnBackPressedDispatcher().addCallback(new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                finish();
            }
        });

        setContentView(R.layout.activity_main);

        binding = ActivityMainBinding.inflate(getLayoutInflater());

        // 用反射获取更大的底部导航栏item数量
        ReflectUtils.setField(binding.bottomNavigationView, "MAX_ITEM_COUNT", Integer.valueOf(10));
        Log.i(TAG, "maxItemCount=" + binding.bottomNavigationView.getMaxItemCount());
        binding.bottomNavigationView.inflateMenu(R.menu.bottom_nav_menu);

        View root = binding.getRoot();
        setContentView(root);

        binding.back.setOnClickListener(v -> {
            finish();
        });

        View.OnClickListener onRewardListener = (view) -> new RewardDialog(view.getContext()).show();

        binding.reward.setOnClickListener(onRewardListener);
        binding.rewardText.setOnClickListener(onRewardListener);
        binding.me.setOnClickListener(onRewardListener);

        Map<Integer, Fragment> fragments = new HashMap<>();
        fragments.put(R.id.navigation_auto_grant, new AppAutoGrantFragment());
        fragments.put(R.id.navigation_accessibility, new AppKeepAccessibilityFragment());
        fragments.put(R.id.navigation_gesture, new AppGestureFragment());
        fragments.put(R.id.navigation_app_debug, new AppDebugFragment());
        fragments.put(R.id.navigation_user_app, new UserAppFragment());
        fragments.put(R.id.navigation_app_icon_editor, new AppIconEditorFragment());
        fragments.put(R.id.navigation_experimental_feature, new ExperimentalFeatureFragment());
        fragments.put(R.id.navigation_syslog, new HappySoundFragment());

        binding.bottomNavigationView.setOnItemSelectedListener(item -> {
            Fragment fragment = fragments.get(item.getItemId());
            if (fragment == null) {
                return false;
            }
            getSupportFragmentManager().beginTransaction().replace(R.id.content, fragment).commit();
            if (fragment instanceof Titleable) {
                binding.title.setText(((Titleable) fragment).getTitle());
            }
            return true;
        });

        binding.bottomNavigationView.setOnItemReselectedListener(item -> {
            Fragment fragment = fragments.get(item.getItemId());
            if (fragment == null) {
                return;
            }
            try {
                Fragment newedInstance = fragment.getClass().newInstance();
                fragments.put(item.getItemId(), newedInstance);
                getSupportFragmentManager().beginTransaction().replace(R.id.content, newedInstance).commit();

                if (fragment instanceof Titleable) {
                    binding.title.setText(((Titleable) fragment).getTitle());
                }
            } catch (Throwable e) {
                throw new RuntimeException(e);
            }
        });

        if (menuId > 0) {
            binding.bottomNavigationView.setSelectedItemId(menuId);
        } else {
            binding.bottomNavigationView.setSelectedItemId(R.id.navigation_auto_grant);
        }

        AppSettings.resetContinuous();

    }


}