package io.github.netamade.adapter;

import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.textview.MaterialTextView;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import io.github.netamade.R;
import io.github.netamade.entity.AppInfo;

public class MultiSelectionAppPickerAdapter extends RecyclerView.Adapter<MultiSelectionAppPickerAdapter.ViewHolder> {
    private final Context context;
    private final List<AppInfo> appInfos;
    private final Set<AppInfo> selectedApps;
    private OnSelectionChangedListener onSelectionChangedListener;

    public interface OnSelectionChangedListener {
        void onSelectionChanged(List<AppInfo> selectedApps);
    }

    public MultiSelectionAppPickerAdapter(Context context, List<AppInfo> appInfos) {
        this.context = context;
        this.appInfos = appInfos;
        this.selectedApps = new HashSet<>();
    }

    public void setOnSelectionChangedListener(OnSelectionChangedListener listener) {
        this.onSelectionChangedListener = listener;
    }

    public List<AppInfo> getSelectedApps() {
        return new ArrayList<>(selectedApps);
    }

    public void selectAll() {
        selectedApps.clear();
        selectedApps.addAll(appInfos);
        notifyDataSetChanged();
        notifySelectionChanged();
    }

    public void selectNone() {
        selectedApps.clear();
        notifyDataSetChanged();
        notifySelectionChanged();
    }

    private void notifySelectionChanged() {
        if (onSelectionChangedListener != null) {
            onSelectionChangedListener.onSelectionChanged(getSelectedApps());
        }
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.layout_multi_selection_app_picker_item, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        AppInfo appInfo = appInfos.get(position);
        
        holder.appName.setText(appInfo.getAppName());
        holder.packageName.setText(appInfo.getPackageName());
        
        // 设置应用图标
        if (appInfo.getIcon() != null) {
            holder.appIcon.setImageDrawable(appInfo.getIcon());
        } else {
            // 尝试从包管理器获取图标
            try {
                PackageManager pm = context.getPackageManager();
                Drawable icon = pm.getApplicationIcon(appInfo.getPackageName());
                holder.appIcon.setImageDrawable(icon);
            } catch (PackageManager.NameNotFoundException e) {
                holder.appIcon.setImageResource(android.R.drawable.sym_def_app_icon);
            }
        }
        
        // 显示系统应用标识
        if (appInfo.isSystem()) {
            holder.systemIndicator.setVisibility(View.VISIBLE);
        } else {
            holder.systemIndicator.setVisibility(View.GONE);
        }
        
        // 显示主Activity
        if (appInfo.hasShortcuts() && !appInfo.getShortcuts().isEmpty()) {
            holder.activityName.setText(appInfo.getShortcuts().get(0).getActivityClass());
            holder.activityName.setVisibility(View.VISIBLE);
        } else {
            holder.activityName.setVisibility(View.GONE);
        }
        
        // 设置选中状态
        holder.checkbox.setChecked(selectedApps.contains(appInfo));
        
        // 设置点击监听器
        View.OnClickListener clickListener = v -> {
            if (selectedApps.contains(appInfo)) {
                selectedApps.remove(appInfo);
            } else {
                selectedApps.add(appInfo);
            }
            holder.checkbox.setChecked(selectedApps.contains(appInfo));
            notifySelectionChanged();
        };
        
        holder.itemView.setOnClickListener(clickListener);
        holder.checkbox.setOnClickListener(clickListener);
    }

    @Override
    public int getItemCount() {
        return appInfos.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        CheckBox checkbox;
        ImageView appIcon;
        MaterialTextView appName;
        MaterialTextView packageName;
        MaterialTextView activityName;
        MaterialTextView systemIndicator;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            checkbox = itemView.findViewById(R.id.checkbox);
            appIcon = itemView.findViewById(R.id.appIcon);
            appName = itemView.findViewById(R.id.appName);
            packageName = itemView.findViewById(R.id.packageName);
            activityName = itemView.findViewById(R.id.activityName);
            systemIndicator = itemView.findViewById(R.id.systemIndicator);
        }
    }
}
