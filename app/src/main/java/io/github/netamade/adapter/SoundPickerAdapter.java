package io.github.netamade.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RadioButton;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.textview.MaterialTextView;

import java.util.List;

import io.github.netamade.R;

public class SoundPickerAdapter extends RecyclerView.Adapter<SoundPickerAdapter.SoundViewHolder> {
    private final Context context;
    private final List<String> soundFiles;
    private int selectedPosition = -1;
    private OnSoundActionListener onSoundActionListener;

    public interface OnSoundActionListener {
        void onSoundSelected(String soundFile, int position);
        void onPlaySound(String soundFile);
        void onDeleteSound(String soundFile, int position);
    }

    public SoundPickerAdapter(Context context, List<String> soundFiles) {
        this.context = context;
        this.soundFiles = soundFiles;
    }

    public void setOnSoundActionListener(OnSoundActionListener listener) {
        this.onSoundActionListener = listener;
    }

    public String getSelectedSoundFile() {
        if (selectedPosition >= 0 && selectedPosition < soundFiles.size()) {
            return soundFiles.get(selectedPosition);
        }
        return null;
    }

    public void setSelectedSoundFile(String soundFile) {
        int position = soundFiles.indexOf(soundFile);
        if (position >= 0) {
            int oldPosition = selectedPosition;
            selectedPosition = position;
            if (oldPosition >= 0) {
                notifyItemChanged(oldPosition);
            }
            notifyItemChanged(selectedPosition);
        }
    }

    @NonNull
    @Override
    public SoundViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_sound_picker, parent, false);
        return new SoundViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull SoundViewHolder holder, int position) {
        String soundFile = soundFiles.get(position);
        
        holder.tvFileName.setText(soundFile);
        holder.radioButton.setChecked(position == selectedPosition);
        
        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            int oldPosition = selectedPosition;
            selectedPosition = position;
            
            if (oldPosition >= 0) {
                notifyItemChanged(oldPosition);
            }
            notifyItemChanged(selectedPosition);
            
            if (onSoundActionListener != null) {
                onSoundActionListener.onSoundSelected(soundFile, position);
            }
        });
        
        holder.radioButton.setOnClickListener(v -> holder.itemView.performClick());
        
        // 播放按钮
        holder.btnPlay.setOnClickListener(v -> {
            if (onSoundActionListener != null) {
                onSoundActionListener.onPlaySound(soundFile);
            }
        });
        
        // 删除按钮
        holder.btnDelete.setOnClickListener(v -> {
            if (onSoundActionListener != null) {
                onSoundActionListener.onDeleteSound(soundFile, position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return soundFiles.size();
    }

    public void removeItem(int position) {
        if (position >= 0 && position < soundFiles.size()) {
            soundFiles.remove(position);
            
            // 调整选中位置
            if (selectedPosition == position) {
                selectedPosition = -1;
            } else if (selectedPosition > position) {
                selectedPosition--;
            }
            
            notifyItemRemoved(position);
            notifyItemRangeChanged(position, soundFiles.size() - position);
        }
    }

    public static class SoundViewHolder extends RecyclerView.ViewHolder {
        RadioButton radioButton;
        MaterialTextView tvFileName;
        MaterialButton btnPlay;
        MaterialButton btnDelete;

        public SoundViewHolder(@NonNull View itemView) {
            super(itemView);
            radioButton = itemView.findViewById(R.id.radioButton);
            tvFileName = itemView.findViewById(R.id.tvFileName);
            btnPlay = itemView.findViewById(R.id.btnPlay);
            btnDelete = itemView.findViewById(R.id.btnDelete);
        }
    }
}
