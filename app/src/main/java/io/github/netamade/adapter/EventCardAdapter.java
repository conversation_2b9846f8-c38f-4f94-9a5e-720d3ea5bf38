package io.github.netamade.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.materialswitch.MaterialSwitch;
import com.google.android.material.textview.MaterialTextView;

import java.io.File;
import java.util.List;

import io.github.netamade.R;
import io.github.netamade.service.SyslogEvent;

public class EventCardAdapter extends RecyclerView.Adapter<EventCardAdapter.EventCardViewHolder> {
    private final Context context;
    private final List<SyslogEvent> events;
    private OnEventChangeListener onEventChangeListener;
    private OnSoundSelectListener onSoundSelectListener;

    public interface OnEventChangeListener {
        void onEventEnabledChanged(SyslogEvent event, boolean enabled);
        void onEventUpdated();
    }

    public interface OnSoundSelectListener {
        void onSoundSelect(SyslogEvent event, int position);
    }

    public EventCardAdapter(Context context, List<SyslogEvent> events) {
        this.context = context;
        this.events = events;
    }

    public void setOnEventChangeListener(OnEventChangeListener listener) {
        this.onEventChangeListener = listener;
    }

    public void setOnSoundSelectListener(OnSoundSelectListener listener) {
        this.onSoundSelectListener = listener;
    }

    @NonNull
    @Override
    public EventCardViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_event_card, parent, false);
        return new EventCardViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull EventCardViewHolder holder, int position) {
        SyslogEvent event = events.get(position);
        
        // 设置描述
        holder.tvDescription.setText(event.getDescription());
        
        // 设置标签
        holder.tvTag.setText(event.getTag());
        
        // 设置消息
        holder.tvMessage.setText(event.getMsg());
        
        // 设置音频文件显示
        if (event.getSound() != null && !event.getSound().isEmpty()) {
            File soundFile = new File(event.getSound());
            holder.tvSoundFile.setText(soundFile.getName());
        } else {
            holder.tvSoundFile.setText("点击选择播放的音频");
        }
        
        // 设置开关状态
        holder.switchEnabled.setOnCheckedChangeListener(null); // 先清除监听器避免触发
        holder.switchEnabled.setChecked(event.isEnabled());
        holder.switchEnabled.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                event.setEnabled(isChecked);
                if (onEventChangeListener != null) {
                    onEventChangeListener.onEventEnabledChanged(event, isChecked);
                }
            }
        });
        
        // 设置音频选择点击事件
        holder.layoutSoundSelector.setOnClickListener(v -> {
            if (onSoundSelectListener != null) {
                onSoundSelectListener.onSoundSelect(event, position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return events.size();
    }

    public static class EventCardViewHolder extends RecyclerView.ViewHolder {
        MaterialTextView tvDescription;
        MaterialTextView tvTag;
        MaterialTextView tvMessage;
        MaterialTextView tvSoundFile;
        MaterialSwitch switchEnabled;
        View layoutSoundSelector;

        public EventCardViewHolder(@NonNull View itemView) {
            super(itemView);
            tvDescription = itemView.findViewById(R.id.tvDescription);
            tvTag = itemView.findViewById(R.id.tvTag);
            tvMessage = itemView.findViewById(R.id.tvMessage);
            tvSoundFile = itemView.findViewById(R.id.tvSoundFile);
            switchEnabled = itemView.findViewById(R.id.switchEnabled);
            layoutSoundSelector = itemView.findViewById(R.id.layoutSoundSelector);
        }
    }
}
