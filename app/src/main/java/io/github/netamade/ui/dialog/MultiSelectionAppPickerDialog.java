package io.github.netamade.ui.dialog;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.android.material.dialog.MaterialAlertDialogBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import io.github.netamade.adapter.MultiSelectionAppPickerAdapter;
import io.github.netamade.databinding.DialogMultiSelectionAppPickerBinding;
import io.github.netamade.entity.AppIconItem;
import io.github.netamade.entity.AppInfo;
import io.github.netamade.util.ApkUtils;

public class MultiSelectionAppPickerDialog {
    private final Context context;
    private DialogMultiSelectionAppPickerBinding binding;
    private AlertDialog dialog;
    private MultiSelectionAppPickerAdapter adapter;
    private List<AppInfo> appInfos;
    private OnAppsSelectedListener onAppsSelectedListener;
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();
    private final Handler mainHandler = new Handler(Looper.getMainLooper());

    public interface OnAppsSelectedListener {
        void onAppsSelected(List<AppIconItem> selectedApps);
    }

    public MultiSelectionAppPickerDialog(@NonNull Context context) {
        this.context = context;
        this.appInfos = new ArrayList<>();
        initDialog();
    }

    private void initDialog() {
        binding = DialogMultiSelectionAppPickerBinding.inflate(LayoutInflater.from(context));

        adapter = new MultiSelectionAppPickerAdapter(context, appInfos);
        adapter.setOnSelectionChangedListener(selectedApps -> {
            // 可以在这里更新选择状态显示
        });

        binding.recyclerView.setLayoutManager(new LinearLayoutManager(context));
        binding.recyclerView.setAdapter(adapter);

        binding.buttonSelectAll.setOnClickListener(v -> adapter.selectAll());
        binding.buttonSelectNone.setOnClickListener(v -> adapter.selectNone());
    }

    public void setOnAppsSelectedListener(OnAppsSelectedListener listener) {
        this.onAppsSelectedListener = listener;
    }

    public void show() {
        MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(context);
        builder.setTitle("选择要启动的应用")
                .setView(binding.getRoot())
                .setPositiveButton("确定", (dialogInterface, i) -> {
                    List<AppInfo> selectedApps = adapter.getSelectedApps();
                    List<AppIconItem> appIconItems = convertToAppIconItems(selectedApps);
                    if (onAppsSelectedListener != null) {
                        onAppsSelectedListener.onAppsSelected(appIconItems);
                    }
                })
                .setNegativeButton("取消", null);

        dialog = builder.create();
        dialog.show();

        // Load main screen apps by default
        loadMainScreenApps();
    }

    private List<AppIconItem> convertToAppIconItems(List<AppInfo> appInfos) {
        List<AppIconItem> appIconItems = new ArrayList<>();
        for (AppInfo appInfo : appInfos) {
            AppIconItem appIconItem = new AppIconItem();
            appIconItem.setAppName(appInfo.getAppName());
            appIconItem.setPackageName(appInfo.getPackageName());

            // Try to get the main activity
            if (appInfo.hasShortcuts() && !appInfo.getShortcuts().isEmpty()) {
                appIconItem.setClassName(appInfo.getShortcuts().get(0).getActivityClass());
            }

            appIconItem.setUsed(true);
            appIconItem.setCloudConfigFlag(true);
            appIconItem.setRedPointFlag(false);
            appIconItem.setNewAdd(true);
            appIconItem.setIconDrawable(appInfo.getIcon());
            appIconItem.setSystemApp(appInfo.isSystem());

            appIconItems.add(appIconItem);
        }
        return appIconItems;
    }

    private void loadMainScreenApps() {
        showLoading(true);
        executorService.execute(() -> {
            List<AppInfo> apps = loadAppsForUser();
            mainHandler.post(() -> {
                appInfos.clear();
                appInfos.addAll(apps);
                adapter.notifyDataSetChanged();
                showLoading(false);
            });
        });
    }


    private void showLoading(boolean show) {
        binding.progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        binding.recyclerView.setVisibility(show ? View.GONE : View.VISIBLE);
    }

    private List<AppInfo> loadAppsForUser() {
        List<AppInfo> apps = new ArrayList<>();
        PackageManager pm = context.getPackageManager();
        List<PackageInfo> installedPackages = pm.getInstalledPackages(
                PackageManager.GET_ACTIVITIES | PackageManager.GET_SERVICES);

        for (PackageInfo packageInfo : installedPackages) {
            AppInfo appInfo = ApkUtils.parseApk(pm, packageInfo);
            apps.add(appInfo);
        }

        return apps;
    }

}
