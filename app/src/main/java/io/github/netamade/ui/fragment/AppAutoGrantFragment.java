package io.github.netamade.ui.fragment;

import android.content.Context;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.preference.Preference;

import java.util.List;

import io.github.netamade.AppContext;
import io.github.netamade.AppSettings;
import io.github.netamade.GrantActivity;
import io.github.netamade.GrantOption;
import io.github.netamade.MainActivity;
import io.github.netamade.R;
import io.github.netamade.entity.AppIconItem;
import io.github.netamade.ui.dialog.MultiSelectionAppPickerDialog;
import io.github.netamade.util.CaptureRunHelper;

public class AppAutoGrantFragment extends BasePreferenceFragment {


    public AppAutoGrantFragment() {
        super(AppContext.applicationContext().getString(R.string.auto_grant));
    }


    @Override
    public void onCreatePreferences(@Nullable Bundle savedInstanceState, @Nullable String rootKey) {
        addPreferencesFromResource(R.xml.app_auto_grant);

        findPreference("grant_now").setOnPreferenceClickListener(preference -> {

            Context context = preference.getContext();
            CaptureRunHelper.from(MainActivity.class).run(() -> {

                GrantActivity.launch(context, GrantOption.builder()
                        .forceGrant(true)
                        .skipRevokePermission(false)
                        .clearTaskBeforeLaunch(true)
                        .targetMap(null)
                        .launchMapDelay(5)
                        .countdownSeconds(0)
                        .unlockSwckeyState(false)
                        .build());
            });

            return true;
        });

        findPreference("countdown_grant").setOnPreferenceClickListener(preference -> {

            Context context = preference.getContext();
            GrantActivity.launch(context, GrantOption.builder()
                    .forceGrant(true)
                    .skipRevokePermission(false)
                    .clearTaskBeforeLaunch(true)
                    .targetMap(null)
                    .launchMapDelay(5)
                    .countdownSeconds(180)
                    .unlockSwckeyState(false)
                    .build());

            return true;
        });

        // 添加应用选择器的点击监听器
        Preference thirdPartyAppsPreference = findPreference("auto_grant_selected_third_party_apps_screen_on");
        if (thirdPartyAppsPreference != null) {
            thirdPartyAppsPreference.setOnPreferenceClickListener(preference -> {
                showThirdPartyAppPicker();
                return true;
            });
        }
    }

    private void showThirdPartyAppPicker() {
        Context context = getContext();
        if (context == null) return;

        MultiSelectionAppPickerDialog dialog = new MultiSelectionAppPickerDialog(context);
        dialog.setOnAppsSelectedListener(selectedApps -> {
            // 保存选中的应用到设置中
            AppSettings.getScreenOn().setSelectedThirdPartyApps(selectedApps);

            // 更新设置项的摘要显示
            updateThirdPartyAppsSummary(selectedApps);
        });
        dialog.show();
    }

    private void updateThirdPartyAppsSummary(List<AppIconItem> selectedApps) {
        Preference thirdPartyAppsPreference = findPreference("auto_grant_selected_third_party_apps_screen_on");
        if (thirdPartyAppsPreference != null) {
            if (selectedApps.isEmpty()) {
                thirdPartyAppsPreference.setSummary("点击选择要启动的应用");
            } else {
                String summary = "已选择 " + selectedApps.size() + " 个应用";
                if (selectedApps.size() <= 3) {
                    StringBuilder appNames = new StringBuilder();
                    for (int i = 0; i < selectedApps.size(); i++) {
                        if (i > 0) appNames.append(", ");
                        appNames.append(selectedApps.get(i).getAppName());
                    }
                    summary = appNames.toString();
                }
                thirdPartyAppsPreference.setSummary(summary);
            }
        }
    }

    private void refreshData() {
        // 刷新应用选择的摘要显示
        List<AppIconItem> selectedApps = AppSettings.getScreenOn().getSelectedThirdPartyApps();
        updateThirdPartyAppsSummary(selectedApps);
    }


    @Override
    public void onResume() {
        super.onResume();
        refreshData();
    }


}
