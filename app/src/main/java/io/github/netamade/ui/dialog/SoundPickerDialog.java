package io.github.netamade.ui.dialog;

import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.android.material.dialog.MaterialAlertDialogBuilder;

import java.io.File;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import io.github.netamade.adapter.SoundPickerAdapter;
import io.github.netamade.databinding.DialogSoundPickerBinding;
import io.github.netamade.sound.SoundManager;
import io.github.netamade.ui.dialog.FilePickerDialog;

public class SoundPickerDialog implements SoundPickerAdapter.OnSoundActionListener {
    private final Context context;
    private DialogSoundPickerBinding binding;
    private AlertDialog dialog;
    private SoundPickerAdapter adapter;
    private List<String> soundFiles;
    private OnSoundSelectedListener onSoundSelectedListener;
    private String currentSelectedSound;
    private String currentSearchKeyword = "";
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();
    private final android.os.Handler mainHandler = new android.os.Handler(android.os.Looper.getMainLooper());

    public interface OnSoundSelectedListener {
        void onSoundSelected(String soundFile);
    }

    public SoundPickerDialog(@NonNull Context context) {
        this.context = context;
        initDialog();
    }

    public void setOnSoundSelectedListener(OnSoundSelectedListener listener) {
        this.onSoundSelectedListener = listener;
    }

    public void setCurrentSelectedSound(String soundFile) {
        this.currentSelectedSound = soundFile;
        if (adapter != null) {
            adapter.setSelectedSoundFile(soundFile);
        }
    }

    private void initDialog() {
        binding = DialogSoundPickerBinding.inflate(LayoutInflater.from(context));

        binding.recyclerView.setLayoutManager(new LinearLayoutManager(context));

        // 搜索功能
        binding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                currentSearchKeyword = s.toString().trim();
                filterSoundFiles();
            }
        });

        // 添加音频按钮
        binding.btnAddAudio.setOnClickListener(v -> showFilePickerDialog());

        binding.btnCancel.setOnClickListener(v -> dialog.dismiss());
        binding.btnConfirm.setOnClickListener(v -> {
            String selectedSound = adapter.getSelectedSoundFile();
            if (selectedSound != null && onSoundSelectedListener != null) {
                onSoundSelectedListener.onSoundSelected(selectedSound);
            }
            dialog.dismiss();
        });
    }

    public void show() {
        MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(context);
        builder.setTitle("选择音频文件")
                .setView(binding.getRoot());

        dialog = builder.create();
        dialog.show();

        initSoundLibrary();
        loadSoundFiles();
    }

    private void initSoundLibrary() {
        // 初始化音频库（如果需要）
        SoundManager.init();
    }

    private void loadSoundFiles() {
        allSoundFiles = SoundManager.listSounds(null);
        filterSoundFiles();
    }

    private void filterSoundFiles() {
        soundFiles = SoundManager.listSounds(currentSearchKeyword);

        if (soundFiles.isEmpty()) {
            showEmptyState();
        } else {
            showSoundList();
        }
    }

    private void showEmptyState() {
        binding.recyclerView.setVisibility(View.GONE);
        binding.emptyStateLayout.setVisibility(View.VISIBLE);
        binding.btnConfirm.setEnabled(false);
    }

    private void showSoundList() {
        binding.recyclerView.setVisibility(View.VISIBLE);
        binding.emptyStateLayout.setVisibility(View.GONE);

        adapter = new SoundPickerAdapter(context, soundFiles);
        adapter.setOnSoundActionListener(this);
        binding.recyclerView.setAdapter(adapter);

        // 设置当前选中的音频
        if (currentSelectedSound != null) {
            adapter.setSelectedSoundFile(currentSelectedSound);
            binding.btnConfirm.setEnabled(true);
        }
    }

    // SoundPickerAdapter.OnSoundActionListener 接口实现
    @Override
    public void onSoundSelected(String soundFile, int position) {
        binding.btnConfirm.setEnabled(true);
    }

    @Override
    public void onPlaySound(String soundFile) {
        SoundManager.playSound(soundFile);
        Toast.makeText(context, "播放: " + soundFile, Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onDeleteSound(String soundFile, int position) {
        new MaterialAlertDialogBuilder(context)
                .setTitle("删除确认")
                .setMessage("确定要删除音频文件 \"" + soundFile + "\" 吗？")
                .setPositiveButton("删除", (dialog, which) -> {
                    SoundManager.deleteSound(soundFile);
                    adapter.removeItem(position);

                    if (soundFiles.isEmpty()) {
                        showEmptyState();
                    } else if (adapter.getSelectedSoundFile() == null) {
                        binding.btnConfirm.setEnabled(false);
                    }

                    Toast.makeText(context, "已删除: " + soundFile, Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton("取消", null)
                .show();
    }

    private void showFilePickerDialog() {
        FilePickerDialog filePickerDialog = new FilePickerDialog(context);
        filePickerDialog.setFileExtensionFilter(new HashSet<>(Arrays.asList("mp3", "wav", "m4a", "aac")));
        filePickerDialog.setOnFileSelectedListener(this::copyAudioFile);
        filePickerDialog.show();
    }

    private void copyAudioFile(File sourceFile) {
        if (sourceFile == null || !sourceFile.exists()) {
            Toast.makeText(context, "文件不存在", Toast.LENGTH_SHORT).show();
            return;
        }

        // 检查文件扩展名
        String fileName = sourceFile.getName().toLowerCase();
        if (!fileName.endsWith(".mp3") && !fileName.endsWith(".wav") &&
                !fileName.endsWith(".m4a") && !fileName.endsWith(".aac")) {
            Toast.makeText(context, "不支持的音频格式，请选择 MP3、WAV、M4A 或 AAC 文件", Toast.LENGTH_SHORT).show();
            return;
        }

        Toast.makeText(context, "正在复制音频文件...", Toast.LENGTH_SHORT).show();

        executorService.execute(() -> {
            String targetFileName = sourceFile.getName();
            boolean success = SoundManager.copyExternalAudioFile(sourceFile, targetFileName);

            mainHandler.post(() -> {
                if (success) {
                    Toast.makeText(context, "音频文件添加成功", Toast.LENGTH_SHORT).show();
                    // 刷新列表并选中新添加的文件
                    loadSoundFiles();
                    if (adapter != null) {
                        adapter.setSelectedSoundFile(targetFileName);
                        binding.btnConfirm.setEnabled(true);
                    }
                } else {
                    Toast.makeText(context, "音频文件添加失败，可能文件已存在", Toast.LENGTH_SHORT).show();
                }
            });
        });
    }
}
