package io.github.netamade.ui.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.ArrayList;
import java.util.List;

import io.github.netamade.AppContext;
import io.github.netamade.AppSettings;
import io.github.netamade.R;
import io.github.netamade.databinding.LayoutHappySoundBinding;
import io.github.netamade.service.SyslogEvent;

public class HappySoundFragment extends BaseFragment {
    public HappySoundFragment() {
        super(AppContext.applicationContext().getString(R.string.happy_sound));
    }

    private LayoutHappySoundBinding binding;
    private List<SyslogEvent> events = new ArrayList<>(SyslogEvent.ALL_EVENTS);
    ;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        List<SyslogEvent> customizedSyslogEvents = AppSettings.getSyslogEvents();
        if (customizedSyslogEvents != null) {
            events.addAll(customizedSyslogEvents);
        }
        return super.onCreateView(inflater, container, savedInstanceState);
    }
}
