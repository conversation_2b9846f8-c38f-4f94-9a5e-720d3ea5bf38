package io.github.netamade.ui.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.LinearLayoutManager;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import io.github.netamade.AppContext;
import io.github.netamade.AppSettings;
import io.github.netamade.R;
import io.github.netamade.adapter.EventCardAdapter;
import io.github.netamade.databinding.LayoutHappySoundBinding;
import io.github.netamade.service.SyslogEvent;
import io.github.netamade.ui.dialog.AddEventDialog;

public class HappySoundFragment extends BaseFragment implements
        EventCardAdapter.OnEventChangeListener,
        EventCardAdapter.OnSoundSelectListener,
        AddEventDialog.OnEventAddedListener {

    public HappySoundFragment() {
        super(AppContext.applicationContext().getString(R.string.happy_sound));
    }

    private LayoutHappySoundBinding binding;
    private List<SyslogEvent> events = new ArrayList<>();
    private EventCardAdapter adapter;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = LayoutHappySoundBinding.inflate(inflater, container, false);

        initData();
        initViews();

        return binding.getRoot();
    }

    private void initData() {
        // 先添加默认事件
        events.addAll(SyslogEvent.ALL_EVENTS);

        // 再添加自定义事件
        List<SyslogEvent> customizedSyslogEvents = AppSettings.getSyslogEvents();
        if (customizedSyslogEvents != null && !customizedSyslogEvents.isEmpty()) {
            events.addAll(customizedSyslogEvents);
        }
    }

    private void initViews() {
        // 设置RecyclerView
        binding.recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        adapter = new EventCardAdapter(getContext(), events);
        adapter.setOnEventChangeListener(this);
        adapter.setOnSoundSelectListener(this);
        binding.recyclerView.setAdapter(adapter);

        // 设置添加事件按钮
        binding.fabAddEvent.setOnClickListener(v -> showAddEventDialog());

        // 设置重置按钮
        binding.fabReset.setOnClickListener(v -> showResetDialog());
    }

    private void showAddEventDialog() {
        AddEventDialog dialog = new AddEventDialog(getContext());
        dialog.setOnEventAddedListener(this);
        dialog.show();
    }

    private void showResetDialog() {
        new AlertDialog.Builder(getContext())
                .setTitle("重置确认")
                .setMessage("确定要重置到默认设置吗？这将删除所有自定义事件并重置所有开关状态。")
                .setPositiveButton("确定", (dialog, which) -> resetToDefault())
                .setNegativeButton("取消", null)
                .show();
    }

    private void resetToDefault() {
        // 清空自定义事件
        AppSettings.setSyslogEvents(new ArrayList<>());

        // 重新初始化数据
        events.clear();
        events.addAll(SyslogEvent.ALL_EVENTS);

        // 重置所有事件的enabled状态为false
        for (SyslogEvent event : events) {
            event.setEnabled(false);
            event.setSound(null);
        }

        adapter.notifyDataSetChanged();
        Toast.makeText(getContext(), "已重置到默认设置", Toast.LENGTH_SHORT).show();
    }

    // EventCardAdapter.OnEventChangeListener 接口实现
    @Override
    public void onEventEnabledChanged(SyslogEvent event, boolean enabled) {
        saveEventsToSettings();
    }

    @Override
    public void onEventUpdated() {
        saveEventsToSettings();
    }

    // EventCardAdapter.OnSoundSelectListener 接口实现
    @Override
    public void onSoundSelect(SyslogEvent event, int position) {
        // TODO: 这里将来实现SoundPickerDialog
        Toast.makeText(getContext(), "音频选择功能将在后续版本中实现", Toast.LENGTH_SHORT).show();
    }

    // AddEventDialog.OnEventAddedListener 接口实现
    @Override
    public void onEventAdded(SyslogEvent event) {
        events.add(event);
        adapter.notifyItemInserted(events.size() - 1);
        saveEventsToSettings();
        Toast.makeText(getContext(), "自定义事件已添加", Toast.LENGTH_SHORT).show();
    }

    private void saveEventsToSettings() {
        // 分离自定义事件（key以"Custom_"开头的事件）
        List<SyslogEvent> customEvents = new ArrayList<>();
        for (SyslogEvent event : events) {
            if (event.getKey().startsWith("Custom_")) {
                customEvents.add(event);
            }
        }
        AppSettings.setSyslogEvents(customEvents);
    }
}
