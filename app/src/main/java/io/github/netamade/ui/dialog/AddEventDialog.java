package io.github.netamade.ui.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;

import com.google.android.material.textfield.TextInputEditText;

import java.util.UUID;

import io.github.netamade.R;
import io.github.netamade.service.SyslogEvent;

public class AddEventDialog extends AlertDialog {
    private TextInputEditText etDescription;
    private TextInputEditText etTag;
    private TextInputEditText etMessage;
    private OnEventAddedListener onEventAddedListener;

    public interface OnEventAddedListener {
        void onEventAdded(SyslogEvent event);
    }

    public AddEventDialog(@NonNull Context context) {
        super(context);
    }

    public void setOnEventAddedListener(OnEventAddedListener listener) {
        this.onEventAddedListener = listener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        View view = LayoutInflater.from(getContext()).inflate(R.layout.dialog_add_event, null);
        setView(view);
        
        etDescription = view.findViewById(R.id.etDescription);
        etTag = view.findViewById(R.id.etTag);
        etMessage = view.findViewById(R.id.etMessage);
        
        setTitle("添加自定义事件");
        
        setButton(BUTTON_POSITIVE, "确定", (dialog, which) -> {
            String description = etDescription.getText().toString().trim();
            String tag = etTag.getText().toString().trim();
            String message = etMessage.getText().toString().trim();
            
            if (TextUtils.isEmpty(description)) {
                Toast.makeText(getContext(), "请输入事件描述", Toast.LENGTH_SHORT).show();
                return;
            }
            
            if (TextUtils.isEmpty(tag)) {
                Toast.makeText(getContext(), "请输入标签", Toast.LENGTH_SHORT).show();
                return;
            }
            
            if (TextUtils.isEmpty(message)) {
                Toast.makeText(getContext(), "请输入消息", Toast.LENGTH_SHORT).show();
                return;
            }
            
            // 生成随机key
            String key = "Custom_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
            
            SyslogEvent newEvent = new SyslogEvent(key, description, tag, message, null, false);
            
            if (onEventAddedListener != null) {
                onEventAddedListener.onEventAdded(newEvent);
            }
        });
        
        setButton(BUTTON_NEGATIVE, "取消", (dialog, which) -> {
            dismiss();
        });
    }
}
