package io.github.netamade.ui.dialog;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;

import com.google.android.material.dialog.MaterialAlertDialogBuilder;

import java.util.UUID;

import io.github.netamade.databinding.DialogAddEventBinding;
import io.github.netamade.service.SyslogEvent;

public class AddEventDialog {
    private final Context context;
    private DialogAddEventBinding binding;
    private AlertDialog dialog;
    private OnEventAddedListener onEventAddedListener;

    public interface OnEventAddedListener {
        void onEventAdded(SyslogEvent event);
    }

    public AddEventDialog(@NonNull Context context) {
        this.context = context;
        initDialog();
    }

    public void setOnEventAddedListener(OnEventAddedListener listener) {
        this.onEventAddedListener = listener;
    }

    private void initDialog() {
        binding = DialogAddEventBinding.inflate(LayoutInflater.from(context));
    }

    public void show() {
        MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(context);
        builder.setTitle("添加自定义事件")
                .setView(binding.getRoot())
                .setPositiveButton("确定", (dialog, which) -> {
                    String description = binding.etDescription.getText().toString().trim();
                    String tag = binding.etTag.getText().toString().trim();
                    String message = binding.etMessage.getText().toString().trim();

                    if (TextUtils.isEmpty(description)) {
                        Toast.makeText(context, "请输入事件描述", Toast.LENGTH_SHORT).show();
                        return;
                    }

                    if (TextUtils.isEmpty(tag)) {
                        Toast.makeText(context, "请输入标签", Toast.LENGTH_SHORT).show();
                        return;
                    }

                    if (TextUtils.isEmpty(message)) {
                        Toast.makeText(context, "请输入消息", Toast.LENGTH_SHORT).show();
                        return;
                    }

                    // 生成随机key
                    String key = "Custom_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);

                    SyslogEvent newEvent = new SyslogEvent(key, description, tag, message, null, false);

                    if (onEventAddedListener != null) {
                        onEventAddedListener.onEventAdded(newEvent);
                    }
                })
                .setNegativeButton("取消", null);

        dialog = builder.create();
        dialog.show();
    }
}
