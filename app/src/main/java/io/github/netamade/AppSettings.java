package io.github.netamade;

import android.content.Context;
import android.content.SharedPreferences;

import androidx.preference.PreferenceManager;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import io.github.netamade.entity.AppIconItem;
import io.github.netamade.entity.AppShortcut;
import io.github.netamade.service.SyslogEvent;
import lombok.Getter;

/**
 * 应用设置
 */
public class AppSettings {

    public static void setCurrentDirectory(String absolutePath) {
        prefs.edit().putString("file_picker_directory", absolutePath).apply();
    }

    public static String getCurrentDirectory() {
        return prefs.getString("file_picker_directory", "/");
    }

    /**
     * 自动任务的触发事件
     */
    public static class TriggerEventSetting {
        private final SharedPreferences prefs;
        private final String suffix;

        public TriggerEventSetting(SharedPreferences prefs, String suffix) {
            this.prefs = prefs;
            this.suffix = suffix;
        }


        public boolean isEnabled() {
            return prefs.getBoolean("auto_grant_enabled" + suffix, false);
        }

        public boolean isBlockAISpeechNetwork() {
            return prefs.getBoolean("block_aispeech_network" + suffix, false);
        }

        public boolean isLaunchMap() {
            return prefs.getBoolean("auto_grant_launch_map" + suffix, false);
        }

        public boolean isClearTask() {
            return prefs.getBoolean("auto_grant_clear_task" + suffix, false);
        }

        public String getSelectedMap() {
            return prefs.getString("auto_grant_selected_map" + suffix, "none");
        }

        public boolean isUnlockSwckeyState() {
            return prefs.getBoolean("auto_grant_unlock_swckey_state" + suffix, false);
        }

        public int getCountdownSeconds() {
            return prefs.getInt("auto_grant_countdown_seconds" + suffix, 0);
        }

        // 应用启动相关方法
        public boolean isLaunchThirdPartyApps() {
            return prefs.getBoolean("auto_grant_launch_third_party_apps" + suffix, false);
        }

        public int getThirdPartyAppLaunchInterval() {
            return prefs.getInt("auto_grant_third_party_app_interval" + suffix, 3);
        }

        public List<AppIconItem> getSelectedThirdPartyApps() {
            String json = prefs.getString("auto_grant_selected_third_party_apps" + suffix, "");
            if (json.isEmpty()) {
                return new ArrayList<>();
            }
            try {
                Gson gson = new Gson();
                TypeToken<List<AppIconItem>> typeToken = new TypeToken<List<AppIconItem>>() {};
                return gson.fromJson(json, typeToken.getType());
            } catch (Exception e) {
                return new ArrayList<>();
            }
        }

        public void setSelectedThirdPartyApps(List<AppIconItem> apps) {
            if (prefs != null) {
                Gson gson = new Gson();
                String json = gson.toJson(apps);
                prefs.edit().putString("auto_grant_selected_third_party_apps" + suffix, json).apply();
            }
        }

    }

    @Getter
    private static TriggerEventSetting screenOn;

    @Getter
    private static SharedPreferences prefs;

    public static void init(Context context) {
        prefs = PreferenceManager.getDefaultSharedPreferences(context);
        screenOn = new TriggerEventSetting(prefs, "_screen_on");
    }

    public static List<String> getQuickLaunchOrders() {
        String s = prefs.getString("quick_launch_orders", "");
        String[] split = s.split(",");
        if (split.length == 0) {
            return Collections.emptyList();
        }
        return new ArrayList<>(Arrays.asList(split));
    }

    public static void setQuickLaunchOrders(List<String> orders) {
        StringBuilder sb = new StringBuilder();
        for (String order : orders) {
            sb.append(',').append(order);
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(0);
        }
        prefs.edit().putString("quick_launch_orders", sb.toString()).apply();
    }

    /**
     * @return 保活设置中的无障碍服务列表
     */
    public static Set<String> getKeepEnabledAccessibilityServiceIds() {
        return new HashSet<>(prefs.getStringSet("keep_enabled_accessibility_service_ids", Collections.emptySet()));
    }

    /**
     * @return 设置保活设置中的无障碍服务列表
     */
    public static void setKeepEnabledAccessibilityServiceIds(Set<String> serviceIds) {
        prefs.edit().putStringSet("keep_enabled_accessibility_service_ids", serviceIds).apply();
    }


    /**
     * @return 获取上次授权时间
     */
    public static long getLastGrantTime() {
        return prefs.getLong("last_grant_time", 0);
    }

    /**
     * 设置上次授权时间
     */
    public static void setLastGrantTime(long time) {
        prefs.edit().putLong("last_grant_time", time).apply();
    }

    /**
     * 设置上次启动时间
     */
    public static void setLastLaunchTime(long time) {
        prefs.edit().putLong("last_launch_time", time).apply();
    }

    /**
     * @return 获取上次启动时间
     */
    public static long getLastLaunchTime() {
        return getLastLaunchTime(0);
    }

    public static long getLastLaunchTime(long def) {
        return prefs.getLong("last_launch_time", def);
    }

    /**
     * @return 连续启动次数
     */
    public static int getContinuousLaunchTimes() {
        return prefs.getInt("continuous_launch_times", 0);
    }

    /**
     * 设置连续启动次数
     */
    public static void setContinuousLaunchTimes(int times) {
        prefs.edit().putInt("continuous_launch_times", times).apply();
    }

    /**
     * 重置连续启动数据
     */
    public static void resetContinuous() {
        prefs.edit().remove("continuous_launch_times").remove("last_launch_time").apply();
    }

    public static boolean isAutoUnlockSwckey() {
        return prefs.getBoolean("auto_unlock_swckey", false);
    }

    public static boolean isAnyTouchViewInSecondaryDisplayEnabled() {
        return prefs.getBoolean("secondary_display_any_touch_view_enabled", true);
    }

    public static boolean isAnyTouchViewEnabled() {
        return prefs.getBoolean("any_touch_view_enabled", true);
    }

    public static boolean isAutoGrantDoNotRevokeBeforeGrant() {
        return prefs.getBoolean("auto_grant_do_not_revoke_before_grant", false);
    }

    public static boolean isLaunchNoClearTask() {
        return prefs.getBoolean("launch_no_clear_task", true);
    }

    public static void setSatelliteViewEnabled(boolean b) {
        prefs.edit().putBoolean("satellite_view_enabled", b).apply();
    }

    public static int getSatelliteAutoGrantSeconds() {
        return prefs.getInt("satellite_auto_grant_seconds", -1);
    }

    public static boolean isHijackSystemMapOpeningOnce() {
        return prefs.getBoolean("hijack_system_map_opening_once", false);
    }

    public static void setHijackSystemMapOpeningOnce(boolean v) {
        prefs.edit().putBoolean("hijack_system_map_opening_once", v).apply();
    }

    public static String getHijackSystemMapOpeningTargetMap() {
        return prefs.getString("hijack_system_map_opening_target_map", "none");
    }

    public static boolean isSatelliteViewEnabled() {
        return prefs.getBoolean("satellite_view_enabled", false);
    }

    public static boolean isAnyTouchViewShowBackground() {
        return prefs.getBoolean("any_touch_view_show_background", true);
    }

    public static String getAnyTouchViewSwipeUp() {
        return prefs.getString("any_touch_view_swipe_up", "none");
    }

    public static String getAnyTouchViewSwipeDown() {
        return prefs.getString("any_touch_view_swipe_down", "none");
    }

    public static String getAnyTouchViewSwipeLeft() {
        return prefs.getString("any_touch_view_swipe_left", "none");
    }

    public static String getAnyTouchViewDoubleTap() {
        return prefs.getString("any_touch_view_double_tap", "none");
    }

    public static String getAnyTouchViewLongPress() {
        return prefs.getString("any_touch_view_long_press", "none");
    }

    public static String getAnyTouchViewSwipeRight() {
        return prefs.getString("any_touch_view_swipe_right", "none");
    }

    public static Set<String> getAnyTouchViewTriggerArea() {
        return new HashSet<>(prefs.getStringSet("any_touch_view_trigger_area", new HashSet<>(Arrays.asList("left", "right"))));
    }

    public static int getAnyTouchViewHeight() {
        return prefs.getInt("any_touch_view_area_height", 10);
    }

    public static int getAnyTouchViewWidth() {
        return prefs.getInt("any_touch_view_area_width", 30);
    }

    public static Set<String> getAnyTouchAppShortCuts() {
        return prefs.getStringSet("any_touch_view_shortcut", defaultAppShortcuts);
    }

    public static void setAnyTouchAppShortCuts(Set<String> values) {
        prefs.edit().putStringSet("any_touch_view_shortcut", values).apply();
    }

    public static List<SyslogEvent> getSyslogEvents(){
        return null;
    }

    public static void setSyslogEvents(List<SyslogEvent> events){

    }


    public static final Set<String> defaultAppShortcuts = new HashSet<>() {
        {
            add(new AppShortcut(AppContext.applicationContext().getString(R.string.app_name), AppContext.applicationContext().getPackageName(), MainActivity.class.getCanonicalName()).toString());
            add(new AppShortcut(AppContext.applicationContext().getString(R.string.tencent_map), AppContext.applicationContext().getPackageName(), TencentMap.class.getCanonicalName()).toString());
            add(new AppShortcut(AppContext.applicationContext().getString(R.string.baidu_map), AppContext.applicationContext().getPackageName(), BaiduMap.class.getCanonicalName()).toString());
            add(new AppShortcut(AppContext.applicationContext().getString(R.string.amap_map), AppContext.applicationContext().getPackageName(), AMap.class.getCanonicalName()).toString());
        }
    };

}
