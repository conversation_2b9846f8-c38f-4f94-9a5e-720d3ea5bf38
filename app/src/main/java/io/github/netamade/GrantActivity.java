package io.github.netamade;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import android.view.Gravity;
import android.view.WindowManager;
import android.widget.ScrollView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import ernestoyaquello.com.verticalstepperform.Step;
import io.github.netamade.databinding.ActivityGrantBinding;
import io.github.netamade.entity.AppIconItem;
import io.github.netamade.exemptions.Exemptions;
import io.github.netamade.steps.CountdownStep;
import io.github.netamade.steps.StepException;
import io.github.netamade.steps.StepMessage;
import io.github.netamade.steps.WorkflowStep;
import io.github.netamade.ui.window.CountdownFloatingView;
import io.github.netamade.util.CaptureRunHelper;
import io.github.netamade.util.HozonSettings;
import io.github.netamade.util.PermissionUtils;
import io.github.netamade.util.SystemUtils;

@SuppressLint("DefaultLocale")
public class GrantActivity extends BaseActivity implements Handler.Callback {

    public static final String TAG = GrantActivity.class.getSimpleName();

    private CountdownStep countdownStep;

    private boolean everPaused = false;

    @Override
    protected void onStart() {
        super.onStart();
        int displayHeight = SystemUtils.getDisplayHeight();
        int displayWidth = SystemUtils.getDisplayWidth();

        int height = (int) (displayHeight * 0.65);
        int width = (int) (displayWidth * 0.4);

        WindowManager.LayoutParams params = getWindow().getAttributes();
        params.gravity = Gravity.CENTER;
        params.height = height;
        params.width = width;

        this.getWindow().setAttributes(params);
    }

    private ActivityGrantBinding binding;
    private Handler handler;
    private GrantOption option;

    @Override
    protected void onDestroy() {
        super.onDestroy();
        handler.removeCallbacksAndMessages(null);
    }

    public static void launch(Context context, GrantOption option) {
        Runnable runnable = () -> {
            Intent intent = new Intent(context, GrantActivity.class);
            intent.putExtra("option", option);

            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
        };

        if (option.getCountdownSeconds() > 0) {
            countdownRun(context, option.getCountdownSeconds(), runnable);
        } else {
            runnable.run();
        }
    }

    private static boolean isCountdownShowing = false;

    private static void countdownRun(Context context, int countdownSeconds, Runnable runnable) {
        if (isCountdownShowing) {
            return;
        }
        isCountdownShowing = true;

        CountdownFloatingView countdownFloatingView = new CountdownFloatingView(context, countdownSeconds);
        countdownFloatingView.setOnCountdownListener(new CountdownFloatingView.OnCountdownListener() {
            @Override
            public void onCancel() {
                isCountdownShowing = false;
                Toast.makeText(context, R.string.canceled, Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onExecute(boolean reach) {
                isCountdownShowing = false;
                runnable.run();
            }
        });
        countdownFloatingView.show();
    }

    protected GrantOption parseOption(Intent intent) {

        if (!intent.hasExtra("option")) {
            return new GrantOption();
        } else {
            return intent.getParcelableExtra("option");
        }

    }


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.i(TAG, "onCreate");

        handler = new Handler(Looper.getMainLooper(), this);

        Intent intent = getIntent();
        option = parseOption(intent);

        checkShouldGrant();


        binding = ActivityGrantBinding.inflate(getLayoutInflater());


        ScrollView scrollView = binding.stepper.findViewById(ernestoyaquello.com.verticalstepperform.R.id.steps_scroll);
        scrollView.setVerticalScrollBarEnabled(false);
        scrollView.setHorizontalScrollBarEnabled(false);

        setContentView(binding.getRoot());
        binding.confirmButton.setEnabled(false);
        binding.cancelButton.setEnabled(true);

        binding.cancelButton.setOnClickListener((v) -> {
            if (binding.cancelButton.getText().equals(getString(R.string.pause)) || binding.cancelButton.getText().equals(getString(R.string.pause_grant))) {

                everPaused = true;
                binding.cancelButton.setText(R.string.cancel);
                binding.confirmButton.setEnabled(true);
                binding.confirmButton.setText(R.string.continue_countdown);

                countdownStep.suspend();
            } else {
                finish();
            }
        });
        binding.confirmButton.setText(option.getTargetMap() == null ? R.string.close_now : R.string.launch_now);

        binding.confirmButton.setOnClickListener((v) -> {
            if (binding.confirmButton.getText().equals(getString(R.string.launch_now))) {
                countdownStep.suspend();
                launchTargetMap();
            } else if (binding.confirmButton.getText().equals(getString(R.string.close_now))) {
                finish();
            } else {
                binding.confirmButton.setText(option.getTargetMap() == null ? R.string.close_now : R.string.launch_now);
                binding.cancelButton.setText(R.string.pause);
                countdownStep.resume();
            }
        });

        List<Step<?>> steps = createSteps();


        if (option.getTargetMap() == null) {
            // 只授权、不启动目标地图
            countdownStep = new CountdownStep("%d 秒后自动关闭", handler, option.getLaunchMapDelay(), StepMessage.FINISH);
        } else {
            countdownStep = new CountdownStep("%d 秒后启动 " + option.getTargetMap().getName(), handler, option.getLaunchMapDelay(), StepMessage.LAUNCH_TARGET_MAP);
        }
        steps.add(countdownStep);

        binding.stepper
                .setup(null, steps)
                .allowNonLinearNavigation(false)
                .displayStepButtons(false)
                .displayBottomNavigation(false)
                .includeConfirmationStep(false)
//                .confirmationStepTitle(confirmationStepTitle)
//                .lastStepNextButtonText("立即执行")
//                .lastStepCancelButtonText("取消")
                .displayNextButtonInLastStep(false)
                .displayCancelButtonInLastStep(false)
                .init();

    }

    private void checkShouldGrant() {
        long currentTimeMillis = System.currentTimeMillis();
        if (option.isForceGrant()) {
            AppSettings.setLastGrantTime(currentTimeMillis);
            AppSettings.resetContinuous();
            return;
        }

        long lastGrantTime = AppSettings.getLastGrantTime();
        long passedTime = currentTimeMillis - lastGrantTime;

        boolean grant = false;

        if (passedTime > TimeUnit.HOURS.toMillis(4)) {
            // 超过 4 个小时
            grant = true;

        } else {

            long lastLaunchTime = AppSettings.getLastLaunchTime(currentTimeMillis);

            int continuousLaunchTimes = AppSettings.getContinuousLaunchTimes();

            AppSettings.setContinuousLaunchTimes(continuousLaunchTimes + 1);

            long timeDiff = currentTimeMillis - lastLaunchTime;

            if (timeDiff < TimeUnit.SECONDS.toMillis(30)) {

                if (continuousLaunchTimes >= 2) {
                    // 30 秒内连续打开 3 次

                    Toast.makeText(this, R.string.reach_continuous_launch_times, Toast.LENGTH_SHORT).show();

                    grant = true;

                }
            } else {
                AppSettings.setLastLaunchTime(currentTimeMillis);
            }
        }

        if (grant) {
            AppSettings.setLastGrantTime(currentTimeMillis);
            AppSettings.resetContinuous();
        } else {
            finish();
        }
    }

    private void launchTargetMap() {
        binding.cancelButton.setText(R.string.close);
        CaptureRunHelper.run(this, new CaptureRunHelper.Callback() {

            @Override
            public void execute() {
                option.getTargetMap().launch(GrantActivity.this);
            }

            @Override
            public void onFinish(Throwable e) {
                if (!everPaused) {
                    finish();
                }
            }
        });
    }

    private void launchThirdPartyApps() {
        List<AppIconItem> thirdPartyApps = option.getThirdPartyApps();
        if (thirdPartyApps == null || thirdPartyApps.isEmpty()) {
            return;
        }

        int interval = option.getThirdPartyAppLaunchInterval();
        Log.i("GrantActivity", "开始启动应用，共 " + thirdPartyApps.size() + " 个，间隔 " + interval + " 秒");

        Handler handler = new Handler(Looper.getMainLooper());

        for (int i = 0; i < thirdPartyApps.size(); i++) {
            final AppIconItem app = thirdPartyApps.get(i);
            final int index = i;

            handler.postDelayed(() -> {
                try {
                    launchThirdPartyApp(app);
                    Log.i("GrantActivity", "启动应用 [" + (index + 1) + "/" + thirdPartyApps.size() + "]: " + app.getAppName());
                } catch (Exception e) {
                    Log.e("GrantActivity", "启动应用失败: " + app.getAppName(), e);
                }
            }, (long) i * interval * 1000);
        }
    }

    private void launchThirdPartyApp(AppIconItem app) {
        if (app.getPackageName() == null || app.getPackageName().isEmpty()) {
            Log.w("GrantActivity", "应用包名为空，跳过启动: " + app.getAppName());
            return;
        }

        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_MAIN);
        intent.addCategory(Intent.CATEGORY_LAUNCHER);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

        if (app.getClassName() != null && !app.getClassName().isEmpty()) {
            // 使用指定的Activity类名
            intent.setClassName(app.getPackageName(), app.getClassName());
        } else {
            // 尝试获取默认的启动Activity
            intent.setPackage(app.getPackageName());
        }

        try {
            startActivity(intent);
        } catch (Exception e) {
            Log.e("GrantActivity", "启动应用失败: " + app.getAppName() + " (" + app.getPackageName() + ")", e);

            // 如果启动失败，尝试使用包管理器获取默认Activity
            try {
                Intent launchIntent = getPackageManager().getLaunchIntentForPackage(app.getPackageName());
                if (launchIntent != null) {
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    startActivity(launchIntent);
                    Log.i("GrantActivity", "使用默认启动方式成功启动: " + app.getAppName());
                } else {
                    Log.e("GrantActivity", "无法获取应用的启动Intent: " + app.getAppName());
                }
            } catch (Exception e2) {
                Log.e("GrantActivity", "使用默认启动方式也失败: " + app.getAppName(), e2);
            }
        }
    }

    @NonNull
    private List<Step<?>> createSteps() {
        List<Step<?>> steps = new ArrayList<>();
        steps.add(new WorkflowStep("运行条件检测", handler) {
            @Override
            protected void execute() {
                if (!PermissionUtils.hasWriteSecureSettingsPermission()) {
                    throw new StepException("缺少系统设置修改权限 WRITE_SECURE_SETTINGS");
                }
                updateText("已获取到系统设置修改权限 WRITE_SECURE_SETTINGS", false);

                if (!Exemptions.test()) {
                    throw new StepException("系统隐藏 API 权限豁免失败");
                }
                updateText("系统隐藏 API 权限豁免成功", true);

                updateTitle("已具备全部运行权限");
            }
        });

        steps.add(new WorkflowStep("获取当前定位状态", handler) {
            @Override
            protected void execute() {
                boolean locationSwitchChecked = HozonSettings.isLocationSwitchChecked();
                int locationAuthorizedTimeInDays = HozonSettings.getLocationAuthorizedTimeInDays();

                String currentLocationStatus = String.format("系统定位开关%s 定位授权剩余 %d 天", locationSwitchChecked ? "已开启" : "已关闭", locationAuthorizedTimeInDays);

                updateText(currentLocationStatus, true);
                updateTitle(currentLocationStatus, true);

                updateText("ACCESS_COARSE_LOCATION : " + PermissionUtils.checkRuntimePermission(Manifest.permission.ACCESS_COARSE_LOCATION), true);
                updateText("ACCESS_FINE_LOCATION : " + PermissionUtils.checkRuntimePermission(Manifest.permission.ACCESS_FINE_LOCATION), true);

            }
        });

        if (option.isSkipRevokePermission() || !AppSettings.isAutoGrantDoNotRevokeBeforeGrant()) {
            steps.add(new WorkflowStep("撤回定位权限", handler) {
                @Override
                protected void execute() {
                    if (!HozonSettings.setLocationSwitchChecked(false) || HozonSettings.isLocationSwitchChecked()) {
                        throw new StepException("关闭系统定位开关失败");
                    }
                    updateText("成功关闭系统定位开关", true);


                    if (!HozonSettings.setLocationAuthorizedTimeInDays(0) || HozonSettings.getLocationAuthorizedTimeInDays() != 0) {
                        throw new StepException(String.format("修改系统定位授权天数为 %d 天失败, 结果 %d 天", 0, HozonSettings.getLocationAuthorizedTimeInDays()));
                    }
                    updateText("修改系统定位授权天数为 0 天成功", true);

                    String result = null;
                    if ((result = PermissionUtils.revokeRuntimePermission(Manifest.permission.ACCESS_COARSE_LOCATION)) != null) {
                        throw new StepException(String.format("撤回 %s 权限失败: %s", "ACCESS_COARSE_LOCATION", result));
                    }
                    updateText(String.format("撤回 %s 权限成功", "ACCESS_COARSE_LOCATION"), true);


                    if ((result = PermissionUtils.revokeRuntimePermission(Manifest.permission.ACCESS_FINE_LOCATION)) != null) {
                        throw new StepException(String.format("撤回 %s 权限失败: %s", "ACCESS_FINE_LOCATION", result));
                    }
                    updateText(String.format("撤回 %s 权限成功", "ACCESS_FINE_LOCATION"), true);

                }
            });
        }

        steps.add(new WorkflowStep("重新执行定位授权", handler) {
            @Override
            protected void execute() {
                if (!HozonSettings.setLocationSwitchChecked(true) || !HozonSettings.isLocationSwitchChecked()) {
                    throw new StepException("关闭系统定位开关失败");
                }
                updateText("成功打开系统定位开关", true);

                long currentTime = HozonSettings.getLocationAuthorizedTime();
                long targetTime;
                int nextDay = 0;

                do {

                    nextDay = new Random().nextInt(260) + 100;
                    targetTime = TimeUnit.DAYS.toMillis(nextDay);

                } while (Math.abs(targetTime - currentTime) < TimeUnit.DAYS.toMillis(7));


                if (!HozonSettings.setLocationAuthorizedTimeInDays(nextDay) || HozonSettings.getLocationAuthorizedTimeInDays() != nextDay) {
                    throw new StepException(String.format("修改系统定位授权天数为 %d 天失败, 结果 %d 天", nextDay, HozonSettings.getLocationAuthorizedTimeInDays()));
                }

                updateText(String.format("修改系统定位授权时间为 %d 天成功", nextDay), true);

                String currentLocationStatus = String.format("系统定位开关%s 定位授权剩余 %d 天", "已开启", nextDay);
                updateTitle(currentLocationStatus, true);


                String result = null;
                if ((result = PermissionUtils.grantRuntimePermission(Manifest.permission.ACCESS_COARSE_LOCATION)) != null) {
                    throw new StepException(String.format("授予 %s 权限失败: %s", Manifest.permission.ACCESS_COARSE_LOCATION, result));
                }
                updateText(String.format("授予 %s 权限成功", Manifest.permission.ACCESS_COARSE_LOCATION), true);


                if ((result = PermissionUtils.grantRuntimePermission(Manifest.permission.ACCESS_FINE_LOCATION)) != null) {
                    throw new StepException(String.format("授予 %s 权限失败: %s", Manifest.permission.ACCESS_FINE_LOCATION, result));
                }
                updateText(String.format("授予 %s 权限成功", Manifest.permission.ACCESS_FINE_LOCATION), true);

            }
        });

        steps.add(new WorkflowStep("检测授权结果", handler) {
            @Override
            protected void execute() {
                if (!HozonSettings.isLocationSwitchChecked()) {
                    throw new StepException("系统定位开关为关闭状态");
                }
                updateText("系统定位开关已打开", false);

                int locationAuthorizedTimeInDays = HozonSettings.getLocationAuthorizedTimeInDays();
                if (locationAuthorizedTimeInDays <= 0) {
                    throw new StepException("系统定位授权天数为 0 天");
                }
                updateText(String.format("系统定位授权剩余 %d 天", locationAuthorizedTimeInDays), true);


                if (!PermissionUtils.hasRuntimePermission(Manifest.permission.ACCESS_FINE_LOCATION)) {
                    throw new StepException("检测到 ACCESS_FINE_LOCATION 权限异常");
                }
                updateText("ACCESS_FINE_LOCATION 权限正常", true);


                if (!PermissionUtils.hasRuntimePermission(Manifest.permission.ACCESS_COARSE_LOCATION)) {
                    throw new StepException("检测到 ACCESS_COARSE_LOCATION 权限异常");
                }
                updateText("ACCESS_COARSE_LOCATION 权限正常", true);

                updateTitle("顺利完成");

                sendMessage(StepMessage.ENABLED_GRANT_BUTTON);
            }
        });

        if (option.isUnlockSwckeyState()) {
            steps.add(new WorkflowStep("解锁车机方控", handler) {
                @Override
                protected void execute() {
                    Intent intent = new Intent(HozonSettings.ACTION_UPDATE_SWCKEY_STATE);
                    intent.putExtra("lock", false);
                    getContext().sendBroadcast(intent);
                }
            });
        }

        if (option.getThirdPartyApps() != null && !option.getThirdPartyApps().isEmpty()) {
            int thirdPartyAppLaunchInterval = option.getThirdPartyAppLaunchInterval();
            String names = option.getThirdPartyApps().stream().map(AppIconItem::getAppName).collect(Collectors.joining(", "));
            steps.add(new CountdownStep("%d 秒后启动应用:  " + names, handler, thirdPartyAppLaunchInterval, StepMessage.LAUNCH_THIRD_PARTY_APPS));
        }

        return steps;
    }

    @Override
    public boolean handleMessage(@NonNull Message msg) {
        switch (msg.what) {
            case StepMessage.GOTO_NEXT_STEP:
                Step<?> step = (Step<?>) msg.obj;
                int total = binding.stepper.getTotalNumberOfSteps();
                int pos = binding.stepper.getStepPosition(step) + 1;
                if (pos >= 0 && pos < total) {
                    AppContext.post(() -> {
                        binding.stepper.goToStep(pos, true);
                    }, 150);
                }
                break;
            case StepMessage.ENABLED_GRANT_BUTTON:
                binding.confirmButton.setEnabled(true);
                break;
            case StepMessage.FINISH:
                finish();
                break;
            case StepMessage.LAUNCH_TARGET_MAP:
                launchTargetMap();
                break;
            case StepMessage.LAUNCH_THIRD_PARTY_APPS:
                launchThirdPartyApps();
                break;

        }
        return false;
    }
}
