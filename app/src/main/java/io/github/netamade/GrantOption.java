package io.github.netamade;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

import io.github.netamade.entity.AppIconItem;
import io.github.netamade.entity.MapType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 授权参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GrantOption implements Parcelable {
    private boolean forceGrant;
    private boolean clearTaskBeforeLaunch;
    private int launchMapDelay;
    private boolean skipRevokePermission;
    private boolean unlockSwckeyState;
    private int countdownSeconds;
    private MapType targetMap;

    // 应用启动相关字段
    private List<AppIconItem> thirdPartyApps;
    private int thirdPartyAppLaunchInterval; // 启动间隔（秒）

    protected GrantOption(Parcel in) {
        forceGrant = in.readByte() != 0;
        clearTaskBeforeLaunch = in.readByte() != 0;
        launchMapDelay = in.readInt();
        skipRevokePermission = in.readByte() != 0;
        unlockSwckeyState = in.readByte() != 0;
        countdownSeconds = in.readInt();
        String map = in.readString();
        targetMap = map == null ? null : MapType.get(map);

        // 读取应用列表
        thirdPartyApps = new ArrayList<>();
        in.readTypedList(thirdPartyApps, AppIconItem.CREATOR);
        thirdPartyAppLaunchInterval = in.readInt();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeByte((byte) (forceGrant ? 1 : 0));
        dest.writeByte((byte) (clearTaskBeforeLaunch ? 1 : 0));
        dest.writeInt(launchMapDelay);
        dest.writeByte((byte) (skipRevokePermission ? 1 : 0));
        dest.writeByte((byte) (unlockSwckeyState ? 1 : 0));
        dest.writeInt(countdownSeconds);
        dest.writeString(targetMap == null ? null : targetMap.getKey());

        // 写入应用列表
        dest.writeTypedList(thirdPartyApps);
        dest.writeInt(thirdPartyAppLaunchInterval);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<GrantOption> CREATOR = new Creator<GrantOption>() {
        @Override
        public GrantOption createFromParcel(Parcel in) {
            return new GrantOption(in);
        }

        @Override
        public GrantOption[] newArray(int size) {
            return new GrantOption[size];
        }
    };
}